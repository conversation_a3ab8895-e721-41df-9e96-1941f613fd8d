<template>
  <div class="ma-content-block p-4">
    <!-- CRUD 组件 -->
    <ma-crud :options="crud" :columns="columns" ref="crudRef" :row-class="() => ''" :row-selection="{ type: 'none' }">
      <!-- 自定义表格内容 -->
      <template #table="{ data }">
        <a-table :columns="columns" :data="data" :pagination="false" :row-key="record => record.id" :hoverable="false"
          :header-cell-style="{ backgroundColor: '#F5F7F9' }">
          <!-- 自定义表格体 -->
          <template #tbody>
            <tbody>
              <!-- 使用v-for直接在行上循环，避免使用template -->
              <!-- 订单头部行 -->
              <tr v-for="record in data" :key="`header-${record.id}`" class="order-header-row">
                <td colspan="100%" class="order-header-cell">
                  <div class="order-header-content">
                    <div class="order-header-left">
                      <a-checkbox :model-value="selectedRowKeys.includes(record.id)"
                        @change="(checked) => handleSingleOrderSelect(checked, record.id)" class="order-checkbox" />
                      <span class="order-id-text">订单编号 {{ record.order_id }}</span>
                      <a-button shape="circle" size="mini" class="copy-btn-inline"
                        @click="copyOrderId(record.order_id)">
                        <icon-copy />
                      </a-button>
                      <span class="order-time-text">下单时间 {{ formatDate(record.created_at) }}</span>
                      <span class="order-source-text">订单来源 {{ record.source }}</span>
                    </div>
                    <div class="order-header-right">
                      <a-button type="text" size="small" class="add-note-btn">
                        <icon-plus />添加标注
                      </a-button>
                    </div>
                  </div>
                </td>
              </tr>
              <!-- 数据行 -->
              <tr v-for="(record, index) in data" :key="`data-${record.id}`">
                <td v-for="column in columns" :key="`${record.id}-${column.dataIndex}`">
                  <template v-if="column.render">
                    <component :is="column.render({ record, index })" />
                  </template>
                  <template v-else-if="column.slotName">
                    <slot :name="column.slotName" :record="record" :index="index" />
                  </template>
                  <template v-else>{{ record[column.dataIndex] }}</template>
                </td>
              </tr>
            </tbody>
          </template>
        </a-table>
      </template>

      <!-- 商品信息列 -->
      <template #product_info="{ record }">
        <div class="order-header-info">
          <div class="order-header-row-custom">
            <div class="order-header-content-custom">
              <span class="order-time-custom" style="margin-left:30px">订单编号 {{ record.order_id }}</span>
              <span class="order-time-custom">三方订单编号 {{ record.third_party_order_sn || record.thirdPartyOrderSn || '-'
                }}</span>
              <span class="order-time-custom">下单时间 {{ formatDate(record.created_at) }}</span>
              <span class="order-source-custom">
                订单来源 {{ record.source }}
                <span>
                  <i data-v-e58c42d4 class="iconfont icon-tianmao"></i>
                </span>
              </span>
            </div>
          </div>
        </div>
        <div class="product-list" style="position: relative; padding-top: 40px;">
          <div v-for="(product, index) in record.products" :key="index" class="product-item cursor-pointer"
            @click="openProductDetail(product, record)">
            <div class="product-content">
              <a-avatar :size="60" :image-url="product.product_image || '/assets/images/default-product.png'"
                class="product-image"></a-avatar>
              <div class="product-text">
                <div class="font-medium">{{ product.product_name }}</div>
                <div class="text-gray-500 text-xs mt-1">商品编号：{{ product.product_sku }}</div>
                <div class="text-gray-500 text-xs mt-1">系统SKU：{{ product.sku }}</div>
                <div class="text-gray-500 text-xs mt-1">三方SKU：{{ product.third_sku }}</div>
                <div class="text-gray-500 text-xs mt-1" v-if="product.spec">规格：{{ product.spec }}</div>
                <div class="text-gray-500 text-xs mt-1" v-if="product.vendor">商家主体：{{ product.vendor }}</div>
              </div>
            </div>
            <div v-if="index < record.products.length - 1" class="product-divider"></div>
          </div>
        </div>
      </template>
      <!-- 派单状态 -->
      <template #payment_status="{ record }">
        <div class="assignment-status mt-2">
          <a-tag :color="record.assignment_status === 1 ? 'green' : 'red'" size="small">
            {{ record.assignment_status === 1 ? '已派单' : '未派单' }}
          </a-tag>
        </div>
      </template>

      <!-- 单价/数量列 -->
      <template #price_quantity="{ record }">
        <div class="price-list">
          <div v-for="(product, index) in record.products" :key="index" class="price-item">
            <div>¥{{ formatAmount(product.price) }}</div>
            <div class="text-gray-500">x{{ product.quantity }}</div>
            <div v-if="index < record.products.length - 1" class="price-divider"></div>
          </div>

        </div>
      </template>

      <!-- 买家/收货人列 -->
      <template #buyer_info="{ record }">
        <div style="text-align: left">
          <div class="text-gray-500 text-xs mt-1">收货人：{{ record.receiver_name }}</div>
          <div class="text-gray-500 text-xs mt-1">联系电话：{{ record.receiver_phone }}</div>
          <div class="text-gray-500 text-xs mt-1" style="width: 150px">收货地址：{{ record.receiver_address }}</div>
        </div>
      </template>

      <!-- 操作列 -->
      <template #operationBeforeExtend="{ record }">
        <div class="flex justify-center space-x-2">
          <a-button type="text" size="small" @click="openDetails(record)">详情</a-button>
          <a-button type="text" size="small" :disabled="record.assignment_status === 1" :style="{
            color: record.assignment_status === 1 ? '#ff4d4f' : '',
            cursor: record.assignment_status === 1 ? 'not-allowed' : 'pointer'
          }" @click="openAssignOrder(record)">
            派单
          </a-button>
        </div>
      </template>
    </ma-crud>

    <!-- 详情抽屉 -->
    <a-drawer :width="'70%'" :visible="detailVisible" @update:visible="(val) => detailVisible = val"
      @cancel="closeDetail" unmountOnClose :footer="true">
      <template #title>订单详情</template>

      <!-- 底部按钮区域 -->
      <template #footer>
        <div class="flex justify-end">
          <a-button @click="closeDetail" class="mr-2">取消</a-button>
          <a-button type="primary" @click="closeDetail()">确定</a-button>
        </div>
      </template>

      <div class="order-detail-container">
        <!-- 订单详情内容 -->
        <div>
          <div class="text-lg font-bold mb-4">订单信息</div>

          <!-- 订单信息表格 -->
          <div class="order-info-table mb-6">
            <div class="order-info-grid">
              <div class="info-cell">
                <div class="info-label">订单编号</div>
                <div class="info-value">{{ detailRecord.order_id || '-' }}</div>
              </div>
              <div class="info-cell">
                <div class="info-label">三方订单编号</div>
                <div class="info-value">{{ detailRecord.third_party_order_sn || detailRecord.thirdPartyOrderSn || '-' }}
                </div>
              </div>
              <div class="info-cell">
                <div class="info-label">下单时间</div>
                <div class="info-value">{{ formatDate(detailRecord.created_at) || '-' }}</div>
              </div>
              <div class="info-cell">
                <div class="info-label">支付时间</div>
                <div class="info-value">{{ formatDate(detailRecord.payment_time) || '-' }}</div>
              </div>
              <div class="info-cell">
                <div class="info-label">订单来源</div>
                <div class="info-value">{{ detailRecord.source || '-' }}</div>
              </div>
              <div class="info-cell">
                <div class="info-label">订单状态</div>
                <div class="info-value">{{ detailRecord.status === 'paid' ? '已支付' : '待支付' }}</div>
              </div>

              <div class="info-cell">
                <div class="info-label">订单来源</div>
                <div class="info-value">{{ detailRecord.source || '-' }}</div>
              </div>
              <div class="info-cell">
                <div class="info-label">实付金额</div>
                <div class="info-value amount-value">¥{{ formatAmount(detailRecord.paid_amount) || '0.00' }}</div>
              </div>
              <div class="info-cell">
                <div class="info-label">订单备注</div>
                <div class="info-value">{{ detailRecord.remark || '-' }}</div>
              </div>
            </div>
          </div>

          <!-- 商品信息 -->
          <div class="mb-6">
            <div class="text-lg font-bold mb-4">商品信息</div>
            <a-table :hoverable="false" :data="detailRecord.products || []" :bordered="true" :pagination="false"
              class="product-detail-table mb-6">
              <template #columns>
                <a-table-column title="商品名称" data-index="product_name">
                  <template #cell="{ record }">
                    <div class="product-detail-cell">
                      <a-avatar :size="40" :image-url="record.product_image || '/assets/images/default-product.png'"
                        class="product-detail-image"></a-avatar>
                      <span class="product-detail-name">{{ record.product_name || '-' }}</span>
                    </div>
                  </template>
                </a-table-column>
                <a-table-column title="商品规格" data-index="spec" align="center" />
                <a-table-column title="商家" data-index="vendor" align="center" />
                <a-table-column title="单价" data-index="price" align="center">
                  <template #cell="{ record }">¥{{ formatAmount(record.price) }}</template>
                </a-table-column>
                <a-table-column title="购买数量" data-index="quantity" align="center" />
                <a-table-column title="小计" data-index="subtotal" align="center">
                  <template #cell="{ record }">¥{{ formatAmount(record.subtotal) }}</template>
                </a-table-column>
              </template>
            </a-table>
          </div>

          <!-- 收货信息 -->
          <div class="mb-6">
            <div class="text-lg font-bold mb-4">收货信息</div>
            <div class="order-info-table mb-6">
              <div class="order-info-grid">
                <div class="info-cell">
                  <div class="info-label">收货人</div>
                  <div class="info-value">{{ detailRecord.receiver_name || '-' }}</div>
                </div>
                <div class="info-cell">
                  <div class="info-label">联系电话</div>
                  <div class="info-value">{{ detailRecord.receiver_phone || '-' }}</div>
                </div>
                <div class="info-cell">
                  <div class="info-label">收货地址</div>
                  <div class="info-value">{{ detailRecord.receiver_address || '-' }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </a-drawer>

    <!-- 派单抽屉 -->
    <a-drawer :width="'80%'" :visible="assignVisible" @update:visible="(val) => assignVisible = val"
      @cancel="closeAssign" unmountOnClose :footer="true">
      <template #title>派单</template>

      <!-- 底部按钮区域 -->
      <template #footer>
        <div class="flex justify-end">
          <a-button @click="closeAssign" class="mr-2">取消</a-button>
          <a-button type="primary" @click="handleAssignSubmit">确定</a-button>
        </div>
      </template>

      <div class="assign-order-container">
        <!-- 订单信息 -->
        <div class="mb-6">
          <div class="text-lg font-bold mb-4">订单信息</div>
          <div class="order-info-table mb-6">
            <div class="order-info-grid">
              <div class="info-cell">
                <div class="info-label">订单编号</div>
                <div class="info-value">{{ assignRecord.order_id || '-' }}</div>
              </div>
              <div class="info-cell">
                <div class="info-label">下单时间</div>
                <div class="info-value">{{ formatDate(assignRecord.created_at) || '-' }}</div>
              </div>
              <div class="info-cell">
                <div class="info-label">订单来源</div>
                <div class="info-value">{{ assignRecord.source || '-' }}</div>
              </div>
              <div class="info-cell">
                <div class="info-label">订单状态</div>
                <div class="info-value">{{ assignRecord.status === 'paid' ? '已支付' : '待支付' }}</div>
              </div>
              <div class="info-cell">
                <div class="info-label">订单备注</div>
                <div class="info-value">{{ assignRecord.remark || '-' }}</div>
              </div>
              <div class="info-cell">
                <div class="info-label">实付金额</div>
                <div class="info-value amount-value">¥{{ formatAmount(assignRecord.paid_amount) || '0.00' }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 商品信息 -->
        <div class="mb-6">
          <div class="text-lg font-bold mb-4">商品信息</div>
          <a-table :data="assignRecord.products" :bordered="true" :pagination="false" :hoverable="false"
            class="product-detail-table mb-4">
            <template #columns>
              <a-table-column title="商品名称" data-index="product_name">
                <template #cell="{ record }">
                  <div class="product-detail-cell">
                    <a-avatar :size="40" :image-url="record.product_image || '/assets/images/default-product.png'"
                      class="product-detail-image"></a-avatar>
                    <span class="product-detail-name">{{ record.product_name || '-' }}</span>
                  </div>
                </template>
              </a-table-column>
              <a-table-column title="商品规格" data-index="spec" align="center" />
              <a-table-column title="商家" data-index="vendor" align="center" />
              <a-table-column title="单价" data-index="price" align="center">
                <template #cell="{ record }">¥{{ formatAmount(record.price) }}</template>
              </a-table-column>
              <a-table-column title="购买数量" data-index="quantity" align="center" />
              <a-table-column title="小计" data-index="subtotal" align="center">
                <template #cell="{ record }">¥{{ formatAmount(record.subtotal) }}</template>
              </a-table-column>
            </template>
          </a-table>
        </div>

        <!-- 派单表单 -->
        <div class="mb-6">
          <div class="text-base font-medium mb-3">派单信息</div>
          <a-form :model="assignForm" layout="vertical">
            <!-- 服务商(公司) -->
            <a-form-item field="providerId" label="选择服务商" :rules="[{ required: true, message: '请选择服务商' }]">
              <a-select v-model="assignForm.providerId" placeholder="请选择服务商" :loading="loading.provider"
                @change="handleProviderChange" allow-clear>
                <a-option v-for="provider in providerList" :key="provider.id" :value="provider.id">
                  {{ provider.companyName || provider.username }}
                </a-option>
              </a-select>
            </a-form-item>

            <!-- 选择业务员 -->
            <a-form-item v-if="salesmanList.length > 1" field="salesmanId" label="选择业务员"
              :rules="[{ required: true, message: '请选择业务员' }]">
              <a-select v-model="assignForm.salesmanId" placeholder="请选择业务员" :loading="loading.salesman" allow-clear>
                <a-option v-for="salesman in salesmanList" :key="salesman.id" :value="salesman.id">
                  {{ salesman.name }}
                </a-option>
              </a-select>
            </a-form-item>

            <!-- 订单类型 -->
            <!-- <a-form-item
                field="orderType"
                label="订单类型"
                :rules="[{ required: true, message: '请选择订单类型' }]"
              >
                <a-select v-model="assignForm.orderType" placeholder="请选择订单类型">
                  <a-option value="1">普通订单</a-option>
                  <a-option value="2">加急订单</a-option>
                  <a-option value="3">特殊订单</a-option>
                </a-select>
            </a-form-item>-->

            <!-- 选择报备信息 -->
            <!-- <a-form-item
              field="orderReportId"
              label="选择报备信息"
              :rules="[{ required: true, message: '请选择报备信息' }]"
            >
              <a-select
                v-model="assignForm.orderReportId"
                placeholder="请选择报备信息"
                :loading="loading.report"
                allow-clear
                @change="handleReportChange"
              >
                <a-option
                  v-for="report in reportRecords"
                  :key="report.id"
                  :value="report.id"
                >
                  {{ report.customer_name }} - {{ report.platform_name || '未知平台' }} - ¥{{ formatAmount(report.report_amount) }}
                </a-option>
              </a-select>
            </a-form-item> -->

            <!-- 订单费率 -->
            <a-form-item field="rate" label="订单费率 (%)" :rules="[{ required: true, message: '请设置订单费率' }]">
              <a-input-number v-model="assignForm.rate" :min="0" :max="100" :precision="2" placeholder="请输入费率" />
            </a-form-item>

            <!-- 备注 -->
            <a-form-item field="remark" label="备注">
              <a-textarea v-model="assignForm.remark" placeholder="请输入备注信息" :rows="4" />
            </a-form-item>
          </a-form>
        </div>
        <!-- 报备信息 -->
        <div class="mb-6">
          <div class="text-lg font-bold mb-4">已通过审核的报备信息</div>
          <a-table :data="reportRecords" :bordered="true" :pagination="false" :hoverable="false"
            :loading="loading.report" class="report-records-table" :header-cell-style="{ backgroundColor: '#F5F7F9' }">
            <template #columns>
              <a-table-column title="选择" align="center" width="80">
                <template #cell="{ record }">
                  <a-radio :model-value="reprotData.orderReportId === record.id" @change="(checked) => {
                    console.log('单选按钮点击:', record.id, checked);
                    if (checked) {
                      handleReportChange(record.id);
                    }
                  }" />
                </template>
              </a-table-column>
              <a-table-column title="报备单号" data-index="id" align="center" width="180" />
              <!-- <a-table-column title="报备类型" data-index="report_type" align="center" width="120">
                <template #cell="{ record }">
                  <a-tag color="blue">{{ getReportTypeText(record.report_type) }}</a-tag>
                </template>
              </a-table-column> -->
              <a-table-column title="报备人" data-index="provider_company_name" align="center" width="200">
                <template #cell="{ record }">
                  <div class="flex items-center justify-center">
                    <icon-user class="text-blue-500 mr-2" />
                    <span>{{ record.provider_company_name }}</span>
                  </div>
                </template>
              </a-table-column>
              <a-table-column title="客户姓名" data-index="customer_name" align="center" width="200">
                <template #cell="{ record }">
                  <div class="flex items-center justify-center">
                    <icon-user class="text-blue-500 mr-2" />
                    <span>{{ record.recipient_name }}</span>
                  </div>
                </template>
              </a-table-column>
              <a-table-column title="客户区域" data-index="customer_region" align="center" width="120" />
              <a-table-column title="平台" data-index="platform_name" align="center" width="100" />
              <a-table-column title="报备金额" data-index="report_amount" align="center" width="120">
                <template #cell="{ record }">
                  ¥{{ formatAmount(record.report_amount) }}
                </template>
              </a-table-column>
              <a-table-column title="报备时间" data-index="created_at" align="center" width="160">
                <template #cell="{ record }">
                  {{ formatDate(record.created_at) }}
                </template>
              </a-table-column>
              <!-- <a-table-column title="关联订单号" data-index="related_order_number" align="center" width="150">
                <template #cell="{ record }">
                  {{ record.related_order_number || '-' }}
                </template>
              </a-table-column> -->
              <a-table-column title="操作" align="center" width="100">
                <template #cell="{ record }">
                  <div class="flex items-center justify-center">
                    <a-button type="text" size="small" @click="openReportDetailInAssign(record)">详情</a-button>
                  </div>
                </template>
              </a-table-column>
            </template>
            <template #empty>
              <div class="text-center text-gray-500 py-4">
                <icon-info-circle class="mr-2" />暂无已通过审核的报备信息
              </div>
            </template>
          </a-table>
        </div>
      </div>
    </a-drawer>
    <!-- 报备详情弹窗 -->
    <a-modal :width="'80%'" :visible="baobeiVisible" @update:visible="(val) => baobeiVisible = val"
      @cancel="closebaobeiDetail" unmountOnClose :footer="false" title="报备详情" :z-index="2000" :mask-closable="false">
      <div class="report-detail-container">
        <!-- 报备基本信息 -->
        <div class="mb-6">
          <div class="text-lg font-bold mb-4">基本信息</div>
          <div class="order-info-table mb-6">
            <div class="order-info-grid">
              <div class="info-cell">
                <div class="info-label">报备单号</div>
                <div class="info-value">{{ baobeiRecord.report_sn || baobeiRecord.id || '-' }}</div>
              </div>
              <div class="info-cell">
                <div class="info-label">平台</div>
                <div class="info-value">{{ getPlatformName(baobeiRecord.platform_id) || '-' }}</div>
              </div>
              <div class="info-cell">
                <div class="info-label">报备类型</div>
                <div class="info-value">{{ getReportTypeText(baobeiRecord.report_type) || '-' }}</div>
              </div>
              <div class="info-cell">
                <div class="info-label">预计下单时间</div>
                <div class="info-value">{{ formatTimestamp(baobeiRecord.expected_order_time) || '-' }}</div>
              </div>
              <div class="info-cell">
                <div class="info-label">客户名称</div>
                <div class="info-value">{{ baobeiRecord.customer_name || '-' }}</div>
              </div>
              <div class="info-cell">
                <div class="info-label">客户区域</div>
                <div class="info-value">{{ baobeiRecord.customer_region || '-' }}</div>
              </div>
              <div class="info-cell col-span-2">
                <div class="info-label">备注</div>
                <div class="info-value">{{ baobeiRecord.remark || '-' }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 商品信息 -->
        <div class="mb-6">
          <div class="text-lg font-bold mb-4">商品信息</div>
          <a-table :data="baobeiRecord.items || []" :bordered="true" :pagination="false"
            class="product-detail-table mb-6">
            <template #footer>
              <tr class="summary-row">
                <td>
                  <div class="summary-label">合计</div>
                </td>

                <td class="text-center" style="width: 142px;">
                  <div class="summary-value">¥{{ formatAmount(calculateTotal(baobeiRecord.items)) }}</div>
                </td>
              </tr>
            </template>
            <template #columns>
              <a-table-column title="商品图片" data-index="product_image" width="120">
                <template #cell="{ record }">
                  <a-avatar :size="60" shape="square"
                    :image-url="record.product_image || '/assets/images/placeholder.png'"
                    class="product-detail-image"></a-avatar>
                </template>
              </a-table-column>
              <a-table-column title="商品名称" data-index="product_name" />
              <a-table-column title="规格与编码" data-index="specification">
                <template #cell="{ record }">
                  <div>规格：{{ record.specification || '-' }}</div>
                  <div class="text-gray-500 text-sm">编码：{{ record.product_code || '-' }}</div>
                </template>
              </a-table-column>
              <a-table-column title="单价" data-index="unit_price" align="center">
                <template #cell="{ record }">¥{{ formatAmount(record.unit_price) }}</template>
              </a-table-column>
              <a-table-column title="数量" data-index="quantity" align="center" />
              <a-table-column title="报备价" data-index="report_price" align="center">
                <template #cell="{ record }">¥{{ formatAmount(record.report_price || record.unit_price) }}</template>
              </a-table-column>
              <!-- <a-table-column title="小计" data-index="subtotal" align="center">
                <template #cell="{ record }">¥{{ formatAmount(record.subtotal) }}</template>
              </a-table-column>-->
            </template>
          </a-table>
        </div>

        <!-- 收货信息 -->
        <div class="mb-6">
          <div class="text-lg font-bold mb-4">收货信息</div>
          <div class="order-info-table mb-6">
            <div class="order-info-grid">
              <div class="info-cell">
                <div class="info-label">收货人</div>
                <div class="info-value">{{ baobeiRecord.recipient_name || '-' }}</div>
              </div>
              <div class="info-cell">
                <div class="info-label">联系电话</div>
                <div class="info-value">{{ baobeiRecord.contact_phone || '-' }}</div>
              </div>
              <div class="info-cell">
                <div class="info-label">预计交付时间</div>
                <div class="info-value">{{ formatTimestamp(baobeiRecord.expected_shipping_time) || '-' }}</div>
              </div>
              <div class="info-cell">
                <div class="info-label">收货地址</div>
                <div class="info-value">{{ baobeiRecord.shipping_address || '-' }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 审核记录 -->
        <!-- <div class="mb-6" v-if="baobeiRecord.audit_status === 2 || baobeiRecord.audit_status === 3">
          <div class="text-lg font-bold mb-4">审核记录</div>
          <a-table :data="baobeiRecord.audit_records || []" :bordered="true" :pagination="false"
            class="audit-record-table">
            <template #columns>
              <a-table-column title="审核时间" data-index="audit_time">
                <template #cell="{ record }">{{ formatDateTime(record.audit_time) }}</template>
              </a-table-column>
              <a-table-column title="审核人" data-index="auditor" />
              <a-table-column title="审核结果" data-index="result">
                <template #cell="{ record }">
                  <a-tag :color="record.result === 1 ? 'green' : 'red'">{{ record.result === 1 ? '通过' : '驳回' }}</a-tag>
                </template>
              </a-table-column>
              <a-table-column title="备注" data-index="comment" />
            </template>
          </a-table>
        </div> -->
        <div class="dialog-footer">
          <a-button @click="closebaobeiDetail" class="mr-2">关闭</a-button>
        </div>
      </div>
    </a-modal>

    <!-- 商品详情弹窗 -->
    <a-modal :visible="productDetailVisible" @update:visible="(val) => productDetailVisible = val" :width="500"
      title="商品详情" @cancel="closeProductDetail" :footer="false">
      <div class="product-detail-container">
        <div class="flex flex-col items-center mb-4">
          <a-image :src="productDetailRecord.product_image || '/assets/images/default-product.png'" :width="300"
            :height="300" fit="contain" />
        </div>
        <div class="grid grid-cols-1 gap-4">
          <div class="info-item">
            <div class="text-gray-500">商品名称：</div>
            <div class="font-medium">{{ productDetailRecord.product_name || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">商品编号：</div>
            <div>{{ productDetailRecord.product_sku || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">商品规格：</div>
            <div>{{ productDetailRecord.product_spec || '标准规格' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">单价：</div>
            <div class="text-red-500 font-medium">¥{{ formatAmount(productDetailRecord.price) || '0.00' }}</div>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, h } from "vue";
import { Message } from "@arco-design/web-vue";
import { Avatar as AAvatar } from "@arco-design/web-vue";
import orderApi from "@/api/provider/order";
import providerApi from "@/api/master/provider";
import orderAssignmentApi from "@/api/master/orderAssignment";

// CRUD组件引用
const crudRef = ref(null);

// 详情抽屉相关
const detailVisible = ref(false);
const detailRecord = ref({});

// 商品详情弹窗相关
const productDetailVisible = ref(false);
const productDetailRecord = ref({});
// 页面元数据
definePageMeta({
  name: "master-cooperative-ordermanagement-orderallocation",
  path: "/master/cooperative/ordermanagement/orderallocation",
  title: '订单分配'
});

// 派单抽屉相关
const selectedKeys = ref([]);
const selectedRows = ref([]);
const selectedRowKeys = ref([]);

const handleSelect = (keys, rows) => {
  selectedKeys.value = keys;
  selectedRows.value = rows;
};

// 单个订单选择处理
const handleSingleOrderSelect = (checked, orderId) => {
  if (checked) {
    if (!selectedRowKeys.value.includes(orderId)) {
      selectedRowKeys.value.push(orderId);
    }
  } else {
    selectedRowKeys.value = selectedRowKeys.value.filter(id => id !== orderId);
  }
};

// 复制订单ID
const copyOrderId = orderId => {
  navigator.clipboard
    .writeText(orderId)
    .then(() => {
      Message.success("订单编号已复制到剪贴板");
    })
    .catch(() => {
      Message.error("复制失败，请手动复制");
    });
};

const assignVisible = ref(false);
const assignRecord = ref({});
const assignForm = reactive({
  providerId: "", // 服务商ID
  salesmanId: "", // 业务员ID
  orderReportId: "", // 报备信息ID
  orderType: "",
  rate: 0,
  remark: ""
});

// 服务商和业务员相关数据
const providerList = ref([]); // 服务商列表
const salesmanList = ref([]); // 当前选中服务商的业务员列表
const loading = reactive({
  provider: false,
  salesman: false,
  report: false
});

// 报备信息数据
const reportRecords = ref([]);
const selectedReportRecord = ref(null); // 选中的报备信息

// 订单描述数据
const orderDescData = computed(() => [
  {
    label: "订单编号",
    value: assignRecord.value.order_id || "-"
  },
  {
    label: "下单时间",
    value: formatDate(assignRecord.value.created_at) || "-"
  },
  {
    label: "订单来源",
    value: assignRecord.value.source || "-"
  },
  {
    label: "实付金额",
    value: `¥${formatAmount(assignRecord.value.paid_amount) || "0.00"}`
  }
]);

// 订单详情表格列定义
const orderInfoColumns = [
  {
    title: "订单编号",
    dataIndex: "order_id",
    width: "25%"
  },
  {
    title: "订单来源",
    dataIndex: "order_source",
    width: "25%"
  },
  {
    title: "下单时间",
    dataIndex: "created_at",
    width: "25%"
  },
  {
    title: "订单状态",
    dataIndex: "status",
    width: "25%"
  }
];

// 订单详情表格数据
const orderInfoData = computed(() => ({
  order_id: detailRecord.value.order_id || "-",
  order_source: detailRecord.value.source || "-",
  created_at: formatDate(detailRecord.value.created_at) || "-",
  status: detailRecord.value.status === "paid" ? "已支付" : "待支付"
}));

// 商品表格列定义
const productColumns = [
  {
    title: "商品名称",
    dataIndex: "product_name",
    width: "500px",
    align: "center",
    cellAlign: "left",
    render: ({ record }) => {
      return h("div", { class: "product-info-cell" }, [
        h("div", { class: "product-image-wrapper" }, [
          h(AAvatar, {
            size: 40,
            imageUrl:
              record.product_image || "/assets/images/default-product.png",
            class: "product-image-small"
          })
        ]),
        h("div", { class: "product-name-text" }, record.product_name)
      ]);
    }
  },
  {
    title: "商品规格",
    dataIndex: "spec",

    align: "center"
  },
  {
    title: "商家",
    dataIndex: "vendor",

    align: "center"
  },
  {
    title: "单价",
    dataIndex: "price",

    align: "center",
    render: ({ record }) => `¥${formatAmount(record.price)}`
  },
  {
    title: "购买数量",
    dataIndex: "quantity",

    align: "center"
  },
  {
    title: "小计",
    dataIndex: "subtotal",

    align: "center",
    render: ({ record }) => `¥${formatAmount(record.subtotal)}`
  }
];

// 收货信息表格列定义
const receiverColumns = [
  {
    title: "收货人",
    dataIndex: "receiver_name",
    width: "20%"
  },
  {
    title: "联系电话",
    dataIndex: "receiver_phone",
    width: "20%"
  },
  {
    title: "收货地址",
    dataIndex: "receiver_address",
    width: "60%"
  }
];
// 格式化金额
// const formatAmount = amount => {
//   if (!amount && amount !== 0) return "-";
//   return amount.toLocaleString("zh-CN", {
//     minimumFractionDigits: 2,
//     maximumFractionDigits: 2
//   });
// };

// 计算商品总价
const calculateTotal = products => {
  if (!products || !products.length) return 0;
  return products.reduce((total, product) => {
    const price = product.report_price || product.unit_price || 0;
    const quantity = product.quantity || 0;
    return total + price * quantity;
  }, 0);
};
// 收货信息数据
const receiverData = computed(() => ({
  receiver_name: detailRecord.value.receiver_name || "-",
  receiver_phone: detailRecord.value.receiver_phone || "-",
  receiver_address: detailRecord.value.receiver_address || "-"
}));

// 订单来源映射（用于筛选）
const orderSourceMap = {
  '京东': 2,
  '淘宝': 2,
  '拼多多': 2,
  '抖店': 2
};


// 获取订单数据的API函数
const getOrderList = async (params) => {
  try {
    // 转换参数格式以匹配后端接口
    const apiParams = {
      page: params?.page || 1,
      pageSize: params?.limit || 10,
      sortField: 'created_at',
      sortOrder: 'desc'
    };

    // 添加筛选条件
    if (params?.order_id) {
      apiParams.orderNumber = params.order_id;
    }
    if (params?.source) {
      // 将来源文本转换为对应的数值
      const sourceMap = {
        '京东': 2,
        '淘宝': 2,
        '拼多多': 2,
        '抖店': 2
      };
      apiParams.orderSource = sourceMap[params.source] || 2;
    }
    if (params?.created_at && params.created_at.length === 2) {
      apiParams.startTime = params.created_at[0];
      apiParams.endTime = params.created_at[1];
    }
    if (params?.receiver_name) {
      apiParams.receiverName = params.receiver_name;
    }

    // 调用API
    const response = await orderApi.getOrders(apiParams);

    if (response.code === 200) {
      // 格式化数据以匹配前端组件需要的格式
      const formattedItems = response.data.items.map(order => {
        const formattedOrder = orderApi.formatOrderData(order);

        return {
          ...formattedOrder,
          // 添加前端组件需要的字段
          source: order.channel_name || '未知来源',  // 直接使用接口返回的channel_name
          status: order.assignment_status === 2 ? 'paid' : 'unpaid',
          payment_time: order.paid_at,

          // 确保收货信息字段正确
          receiver_name: order.receiver_name,
          receiver_phone: order.receiver_phone,
          receiver_address: order.receiver_address,

          // 确保金额字段正确 - 处理可能的字符串或Decimal类型
          paid_amount: parseFloat((order.paid_amount || 0).toString()),
          total_amount: parseFloat((order.total_amount || 0).toString()),

          // 确保时间字段正确
          created_at: order.created_at,

          // 确保第三方订单编号字段正确
          third_party_order_sn: order.third_party_order_sn,
          thirdPartyOrderSn: order.third_party_order_sn,

          // 确保商品信息中的SKU字段正确
          products: (order.products || []).map(product => ({
            ...product,
            sku: product.sku_code || product.product_sku || '', // 系统SKU
            product_sku: product.sku_code || product.product_sku || '', // 商品编号
            third_sku: product.third_party_sku_id || product.third_sku || '', // 三方SKU
            // 确保价格字段正确
            price: parseFloat((product.price || product.unit_price || 0).toString()),
            subtotal: parseFloat((product.subtotal || product.total_price || 0).toString())
          }))
        };
      });

      return {
        success: true,
        message: "获取订单列表成功",
        code: 200,
        data: {
          items: formattedItems,
          pageInfo: {
            total: response.data.pageInfo.total,
            currentPage: response.data.pageInfo.currentPage,
            totalPage: response.data.pageInfo.totalPage
          }
        }
      };
    } else {
      throw new Error(response.message || '获取订单列表失败');
    }
  } catch (error) {
    console.error('获取订单列表失败:', error);
    Message.error(error.message || '获取订单列表失败');
    return {
      success: false,
      message: error.message || '获取订单列表失败',
      code: -1,
      data: {
        items: [],
        pageInfo: {
          total: 0,
          currentPage: 1,
          totalPage: 1
        }
      }
    };
  }
};


// 派单抽屉内的报备详情相关
const baobeiVisible = ref(false);
const baobeiRecord = ref({});

// 打开派单抽屉内的报备详情
const openReportDetailInAssign = async (record) => {
  try {
    console.log('打开派单抽屉内的报备详情，记录ID:', record.id);
    console.log('当前baobeiVisible状态:', baobeiVisible.value);

    // 调用API获取详细数据
    const response = await orderApi.getOrderReportDetail(record.id);

    console.log('报备详情API响应:', response);

    if (response && response.code === 200) {
      // 使用API返回的详细数据
      baobeiRecord.value = response.data;
      console.log('设置baobeiRecord数据:', baobeiRecord.value);

      // 设置弹窗可见
      baobeiVisible.value = true;
      console.log('设置baobeiVisible为true，当前状态:', baobeiVisible.value);

      Message.success('报备详情数据加载成功');
    } else {
      Message.error(response?.message || '获取报备详情失败');
    }
  } catch (error) {
    console.error('获取报备详情失败:', error);
    Message.error('获取报备详情失败，请稍后再试');
  }
};

const closebaobeiDetail = () => {
  baobeiVisible.value = false;
  baobeiRecord.value = {};
};
// CRUD 配置
const crud = reactive({
  title: "订单分配",
  api: getOrderList, // 使用真实的API函数
  searchSpan: 8,
  searchLabelWidth: "120px",
  column: false,
  // 分页相关配置
  showIndex: false, // 不显示序号
  pageLayout: "fixed",
  page: true, // 启用分页
  pageSize: 10, // 每页显示10条数据
  pageSizes: [10, 20, 50, 100] // 可选的每页条数
});

// 表格列配置
const columns = reactive([
  {
    title: "订单编号",
    dataIndex: "order_id",
    width: 0,
    hide: true,
    search: true,
    searchPlaceholder: "请输入订单编号",
    searchSpan: 6
  },

  {
    title: "商品信息",
    dataIndex: "product_info",
    width: 300,
    align: "left",
    search: false
  },

  {
    title: "派单状态",
    dataIndex: "payment_status",
    width: 120,
    align: "center",
    search: false
  },
  {
    title: "单价/数量",
    dataIndex: "price_quantity",
    width: 120,
    align: "center",
    search: false
  },
  {
    title: "订单来源",
    dataIndex: "source",
    width: 120,
    align: "center",
    search: true,
    formType: "select",
    hide: true,
    dict: {
      data: [
        { label: "全部", value: "" },
        { label: "京东", value: "京东" },
        { label: "淘宝", value: "淘宝" },
        { label: "拼多多", value: "拼多多" },
        { label: "抖店", value: "抖店" }
      ]
    },
    searchSpan: 6
  },
  {
    title: "实付金额",
    dataIndex: "paid_amount",
    width: 120,
    align: "center",
    render: ({ record }) => `¥${formatAmount(record.paid_amount)}`
  },

  {
    title: "收货信息",
    dataIndex: "buyer_info",
    width: 300,
    search: true,
    searchKey: "receiver_name",
    searchPlaceholder: "请输入收货人姓名",
    searchSpan: 6,
    align: "left"
  },
  {
    title: "下单时间",
    dataIndex: "created_at",
    search: true,
    width: 0,
    hide: true,
    formType: "datetimerange",
    searchSpan: 8,
    searchComponent: "a-range-picker",
    searchComponentProps: {
      style: "width: 100%",
      allowClear: true,
      format: "YYYY-MM-DD",
      shortcuts: [
        {
          text: "最近7天",
          value: () => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            return [start, end];
          }
        },
        {
          text: "最近30天",
          value: () => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            return [start, end];
          }
        },
        {
          text: "本月",
          value: () => {
            const end = new Date();
            const start = new Date();
            start.setDate(1);
            return [start, end];
          }
        }
      ]
    }
  },
  {
    title: "操作",
    dataIndex: "operationBeforeExtend",
    width: 150,
    align: "center",
    fixed: "right"
  }
]);

// 打开详情抽屉
const openDetails = record => {
  detailRecord.value = { ...record };
  detailVisible.value = true;
};

// 关闭详情抽屉
const closeDetail = () => {
  detailVisible.value = false;
};

// 打开派单抽屉
const openAssignOrder = record => {
  // 检查派单状态
  if (record.assignment_status === 1) {
    Message.warning('该订单已派单，无法重复派单');
    return;
  }

  assignRecord.value = { ...record };
  assignVisible.value = true;

  console.log('打开派单抽屉，订单信息:', record);
  console.log('订单派单状态:', record.payment_status);

  // 获取服务商列表
  if (providerList.value.length === 0) {
    fetchProviderList();
  }

  // 根据当前订单的三方订单编号获取报备信息
  const thirdPartyOrderSn = record.third_party_order_sn || record.thirdPartyOrderSn;
  console.log('订单的三方订单编号:', thirdPartyOrderSn);

  if (thirdPartyOrderSn) {
    fetchReportRecords(thirdPartyOrderSn);
  } else {
    // 如果没有三方订单编号，清空报备信息列表
    reportRecords.value = [];
    console.log('该订单没有三方订单编号，无法获取报备信息');
  }
};

// 关闭派单抽屉
const closeAssign = () => {
  assignVisible.value = false;
  // 重置表单
  assignForm.providerId = "";
  assignForm.salesmanId = "";
  assignForm.orderReportId = "";
  assignForm.orderType = "";
  assignForm.rate = 0;
  assignForm.remark = "";
  // 清空业务员列表和报备信息选择
  salesmanList.value = [];
  selectedReportRecord.value = null;
};

// 打开商品详情弹窗
const openProductDetail = (product, order) => {
  productDetailRecord.value = {
    ...product,
    order_id: order.order_id,
    created_at: order.created_at,
    source: order.source
  };
  productDetailVisible.value = true;
};

// 关闭商品详情弹窗
const closeProductDetail = () => {
  productDetailVisible.value = false;
};

// 获取服务商列表
const fetchProviderList = async () => {
  try {
    loading.provider = true;
    const response = await providerApi.provider.getList({
      page: 1,
      pageSize: 1000 // 获取所有服务商
    });

    if (response.code === 200 && response.data) {
      providerList.value = response.data.items || [];
      console.log('服务商列表:', providerList.value);
    } else {
      Message.error('获取服务商列表失败');
    }
  } catch (error) {
    console.error('获取服务商列表失败:', error);
    Message.error('获取服务商列表失败');
  } finally {
    loading.provider = false;
  }
};

// 获取报备信息列表
const fetchReportRecords = async (thirdPartyOrderSn = null, platformId = null, providerId = null) => {
  try {
    loading.report = true;

    // 如果没有三方订单编号，清空报备信息列表
    if (!thirdPartyOrderSn) {
      reportRecords.value = [];
      return;
    }

    console.log('根据三方订单编号获取报备信息:', thirdPartyOrderSn);
    console.log('平台ID:', platformId);

    // 构建请求参数 (JSON格式)
    const requestParams = {
      "page": 1,
      "pageSize": 100000,
      "audit_status": 2,
      // "related_order_number":thirdPartyOrderSn,
      // "platform_id": platformId,
      "provider_id": providerId,
      // related_order_number:'20250620091547096002'
      // "platform_id": "194267216928182272",
      // "provider_id":"7341719356239257483",
    };

    // 调用报备列表接口 (使用POST请求)
    const response = await orderApi.getOrderReportListByPost(requestParams);

    console.log('报备信息API响应:', response);

    if (response && response.code === 200) {
      // 处理返回的数据，添加报备类型文本
      const items = (response.data?.items || []).map(item => ({
        ...item,
        report_type_text: getReportTypeText(item.report_type),
        audit_status_text: getAuditStatusText(item.audit_status)
      }));

      reportRecords.value = items;
      console.log('处理后的报备信息列表:', reportRecords.value);
    } else {
      console.error('获取报备信息失败:', response?.message);
      reportRecords.value = [];
      if (response?.message) {
        Message.error(response.message);
      }
    }
  } catch (error) {
    console.error('获取报备信息失败:', error);
    Message.error('获取报备信息失败');
    reportRecords.value = [];
  } finally {
    loading.report = false;
  }
};

// 处理服务商选择变化
const handleProviderChange = async (providerId) => {
  console.log('选择的服务商ID:', providerId);

  // 清空业务员选择和费率
  assignForm.salesmanId = "";
  assignForm.rate = "";
  assignForm.orderReportId = ""; // 清空报备信息选择
  salesmanList.value = [];

  // 获取当前订单的三方订单编号
  const thirdPartyOrderSn = assignRecord.value.third_party_order_sn || assignRecord.value.thirdPartyOrderSn;
  console.log('当前订单的三方订单编号:', thirdPartyOrderSn);

  // 先获取一次报备信息（不带platform_id过滤）
  await fetchReportRecords(thirdPartyOrderSn);

  // 如果没有选择服务商，只获取报备信息，不处理服务商相关逻辑
  if (!providerId) {
    console.log('未选择服务商，只获取报备信息');
    return;
  }

  // 查找选中的服务商
  const selectedProvider = providerList.value.find(p => p.id === providerId);
  if (!selectedProvider) return;

  console.log('选中的服务商:', selectedProvider);

  // 处理业务员数据
  if (selectedProvider.salesmanId && selectedProvider.salesmanName) {
    const salesmanIds = selectedProvider.salesmanId.split(',').map(id => id.trim()).filter(id => id);
    const salesmanNames = selectedProvider.salesmanName.split('，').map(name => name.trim()).filter(name => name);

    console.log('业务员ID列表:', salesmanIds);
    console.log('业务员姓名列表:', salesmanNames);

    // 构建业务员列表
    const salesmen = [];
    for (let i = 0; i < salesmanIds.length; i++) {
      if (salesmanIds[i] && salesmanNames[i]) {
        salesmen.push({
          id: salesmanIds[i],
          name: salesmanNames[i]
        });
      }
    }

    salesmanList.value = salesmen;
    console.log('处理后的业务员列表:', salesmanList.value);

    // 如果只有一个业务员，自动选择该业务员（不显示下拉框）
    if (salesmanList.value.length === 1) {
      assignForm.salesmanId = salesmanList.value[0].id;
      console.log('自动选择唯一业务员:', salesmanList.value[0]);
    } else {
      // 多个业务员时清空选择，让用户手动选择
      assignForm.salesmanId = "";
    }
  }

  // 获取该服务商对应渠道的费率
  if (assignRecord.value?.channel_id) {
    try {
      console.log('获取费率 - 服务商ID:', providerId, '渠道ID:', assignRecord.value.channel_id);
      const response = await providerApi.getProviderChannelRate(providerId, assignRecord.value.channel_id);
      console.log('费率API响应:', response);
      if (response.code === 200 && response.data) {
        // 自动填充费率
        assignForm.rate = response.data.rate;
        console.log('自动获取费率成功:', assignForm.rate);
        Message.success(`已自动获取费率: ${response.data.rate}`);

        // 使用获取到的channel_id作为platform_id重新获取报备信息
        const platformId = response.data.channel_id;
        const providerId = response.data.provider_id;
        console.log('使用platform_id重新获取报备信息:', platformId);
        await fetchReportRecords(thirdPartyOrderSn, platformId, providerId);
      } else {
        console.log('未找到该服务商对应渠道的费率，响应码:', response.code);
      }
    } catch (error) {
      console.error('获取费率失败:', error);
      // 获取失败时不显示错误信息，让用户手动输入费率
    }
  }
};
const reprotData = reactive({
  platform_id: "",
  report_type: "",
  amount: 0,
  expected_order_date: null,
  related_order_id: "",
  expected_delivery_date: null,
  customer_name: "",
  customer_region: "",
  receiver_name: "",
  receiver_phone: "",
  region_codes: [],
  region_names: [],
  detail_address: "",
  remark: "",
  products: []

})
// 处理报备信息选择变化
const handleReportChange = (reportId) => {
  console.log('选择报备信息ID:', reportId);
  console.log('当前报备信息列表:', reportRecords.value);

  if (!reportId) {
    // 清空选择
    reprotData.orderReportId = "";
    selectedReportRecord.value = null;
    console.log('清空报备信息选择');
    return;
  }

  // 1. 更新表单中的报备信息ID，让单选框亮起来
  reprotData.orderReportId = reportId;
  console.log('更新assignForm.orderReportId为:', reprotData.orderReportId);

  // 2. 找到选中的报备信息数据
  const selectedReport = reportRecords.value.find(report => report.id === reportId);
  if (selectedReport) {
    // 保存选中的报备信息完整数据
    selectedReportRecord.value = selectedReport;
    console.log('选中的报备信息完整数据:', selectedReport);
    if (!assignForm.providerId) {
      // 3. 如果报备信息中有服务商ID，自动选择对应的服务商
      if (selectedReport.provider_id) {
        assignForm.providerId = selectedReport.provider_id.toString();
        console.log('自动选择服务商ID:', reprotData.providerId);
        // 触发服务商变化处理，获取业务员列表和费率
        handleProviderChange(selectedReport.provider_id.toString());
      }

      // 显示选择成功的消息
      Message.success(`已选择报备信息: ${selectedReport.customer_name} - ${getReportTypeText(selectedReport.report_type)}`);
    }

  } else {
    console.error('未找到对应的报备信息，reportId:', reportId);
    Message.error('未找到对应的报备信息');
  }
};

// 提交派单
const handleAssignSubmit = async () => {
  try {
    // 验证表单
    if (!assignForm.providerId) {
      Message.error('请选择服务商');
      return;
    }

    // 验证报备信息选择
    if (!reprotData.orderReportId) {
      Message.error('请选择报备信息');
      return;
    }

    // 验证业务员选择
    if (salesmanList.value.length > 1 && !assignForm.salesmanId) {
      Message.error('请选择业务员');
      return;
    }

    if (salesmanList.value.length === 0) {
      Message.error('该服务商没有业务员信息');
      return;
    }

    // 确保有业务员ID（单个业务员自动选择，多个业务员手动选择）
    if (!assignForm.salesmanId && salesmanList.value.length === 1) {
      assignForm.salesmanId = salesmanList.value[0].id;
    }

    if (!assignForm.rate) {
      Message.error('请设置订单费率');
      return;
    }

    // 提交派单数据
    const submitData = {
      order_id: assignRecord.value.order_id,
      order_report_id: reprotData.orderReportId,
      provider_id: assignForm.providerId,
      salesman_id: assignForm.salesmanId,
      rate: assignForm.rate,
      assignment_amount: assignRecord.value.paid_amount, // 指派金额使用订单实付金额
      remark: assignForm.remark,
      assigned_by: JSON.parse(localStorage.getItem("user_master")).id
    };

    console.log("=== 派单提交数据 ===");
    console.log("订单信息:", assignRecord.value);
    console.log("选中的报备信息:", selectedReportRecord.value);
    console.log("表单数据:", assignForm);
    console.log("最终提交数据:", submitData);
    console.log("==================");

    const response = await orderAssignmentApi.createAssignment(submitData);

    if (response.code === 200) {
      Message.success('派单成功');
      closeAssign();
      // 刷新订单列表
      if (crudRef.value) {
        crudRef.value.refresh();
      }
    } else {
      Message.error(response.message || '派单失败');
    }
  } catch (error) {
    console.error('派单失败:', error);
    Message.error(error.message || '派单失败');
  }
};

// 格式化金额
const formatAmount = amount => {
  if (!amount && amount !== 0) return "0.00";
  return parseFloat(amount).toFixed(2);
};

// 格式化日期
const formatDate = date => {
  if (!date) return "";

  // 处理BigInt时间戳
  let timestamp = date;
  if (typeof date === 'bigint') {
    timestamp = Number(date);
  } else if (typeof date === 'string') {
    timestamp = parseInt(date);
  }

  // 如果时间戳是毫秒级别的，直接使用；如果是秒级别的，需要乘以1000
  if (timestamp < 10000000000) {
    timestamp = timestamp * 1000;
  }

  return new Date(timestamp).toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit"
  });
};

// 获取报备类型文本
const getReportTypeText = type => {
  switch (parseInt(type)) {
    case 1:
      return "新客户报备";
    case 2:
      return "老客户续单";
    case 3:
      return "竞争客户转单";
    default:
      return "未知类型";
  }
};

// 获取审核状态文本
const getAuditStatusText = status => {
  switch (parseInt(status)) {
    case 1:
      return "待审核";
    case 2:
      return "审核通过";
    case 3:
      return "已驳回";
    default:
      return "未知状态";
  }
};

// 获取平台名称
const getPlatformName = platformId => {
  const platformMap = {
    "185653478876647424": "天猫",
    "185653478876647425": "京东",
    "185653478876647426": "微信",
    "185653478876647427": "其他",
    "194267216928182272": "自采"
  };
  return platformMap[platformId] || "未知平台";
};

// 格式化时间戳
const formatTimestamp = timestamp => {
  if (!timestamp) return "-";
  // 如果是字符串格式的时间戳，转换为数字
  const time = typeof timestamp === 'string' ? parseInt(timestamp) : timestamp;
  // 如果时间戳是秒级，转换为毫秒级
  const date = new Date(time * 1000);
  return date.toLocaleDateString("zh-CN");
};

// 格式化日期时间
const formatDateTime = datetime => {
  if (!datetime) return "-";
  const date = new Date(datetime);
  return `${date.toLocaleDateString("zh-CN")} ${date.toLocaleTimeString(
    "zh-CN",
    { hour: "2-digit", minute: "2-digit" }
  )}`;
};

// 页面初始化
onMounted(() => {
  // 页面加载时会自动调用api.list方法获取数据
  console.log("订单分配页面初始化完成");
});
</script>

<script>
export default { name: "master-cooperative-orderallocation" };
</script>

<style scoped lang="less">
/* 报备信息表格样式 */
.report-records-table {
  :deep(.arco-table-container) {
    border-radius: 4px;
    overflow: hidden;
  }

  :deep(.arco-table-td) {
    padding: 12px 16px;
  }

  :deep(.arco-table-tr:hover) {
    .arco-table-td {
      background-color: var(--color-fill-2);
    }
  }
}

.order-detail-container {
  padding: 0 16px;
}

.assign-order-container {
  padding: 0 16px;
}

.info-item {
  display: flex;
  margin-bottom: 12px;
}

.info-item .text-gray-500 {
  width: 120px;
  flex-shrink: 0;
}

/* 订单详情表格样式 */
:deep(.arco-table-th) {
  background-color: #f5f5f5 !important;
  text-align: center !important;
  font-weight: bold;
  padding: 10px 16px !important;
}

:deep(.arco-table-td) {
  text-align: center !important;
  padding: 10px 16px !important;
}

/* 表格样式 */
.custom-table {
  width: 100%;
  border-collapse: collapse;
}

/* 表头背景颜色 */
:deep(.arco-table-th) {
  background-color: #f5f7f9 !important;
}

/* 所有单元格背景颜色 */
:deep(.arco-table-td) {
  background-color: #f5f7f9 !important;
}

/* 操作列背景颜色 */
:deep(.arco-table-td:last-child) {
  background-color: #ffffff !important;
}

/* 斑马纹表格固定列背景色 */
:deep(.arco-table-stripe:not(.arco-table-dragging) .arco-table-tr:not(.arco-table-tr-empty):not(.arco-table-tr-summary):nth-child(even) .arco-table-td.arco-table-col-fixed-left::before),
:deep(.arco-table-stripe .arco-table-tr-drag .arco-table-td.arco-table-col-fixed-left::before),
:deep(.arco-table-stripe:not(.arco-table-dragging) .arco-table-tr:not(.arco-table-tr-empty):not(.arco-table-tr-summary):nth-child(even) .arco-table-td.arco-table-col-fixed-right::before),
:deep(.arco-table-stripe .arco-table-tr-drag .arco-table-td.arco-table-col-fixed-right::before) {
  background-color: #ffffff !important;
}

/* 商品详情表格样式 */
.product-detail-table {
  width: 100%;
  border: 1px solid #f0f0f0;
}

:deep(.product-detail-table .arco-table-container) {
  border: none;
}

:deep(.product-detail-table .arco-table-th) {
  background-color: #fafafa;
  border: 1px solid #f0f0f0 !important;
  text-align: center !important;
  padding: 10px 16px !important;
}

:deep(.product-detail-table .arco-table-td) {
  border: 1px solid #f0f0f0 !important;
  padding: 10px 16px !important;
}

.product-detail-cell {
  display: flex;
  align-items: center;
  text-align: left;
  padding: 4px 0;
}

.product-detail-image {
  margin-right: 10px;
  flex-shrink: 0;
}

.product-detail-name {
  font-weight: 500;
  vertical-align: middle;
}

:deep(.product-table .arco-table-container) {
  border: none;
}

:deep(.product-table .arco-table-tr) {
  border: 1px solid #f0f0f0;
}

:deep(.product-table .arco-table-th) {
  border: 1px solid #f0f0f0 !important;
  text-align: center !important;
  background-color: #fafafa;
}

:deep(.product-table .arco-table-td) {
  border: 1px solid #f0f0f0 !important;
}

.product-info-cell {
  display: flex !important;
  align-items: center;
  justify-content: flex-start;
  text-align: left;
  padding: 8px 0;
  width: 100%;
}

.product-image-wrapper {
  display: flex;
  align-items: center;
  margin-right: 10px;
}

.product-image-small {
  width: 40px;
  height: 40px;
  object-fit: cover;
  border-radius: 4px;
  flex-shrink: 0;
}

.product-name-text {
  font-weight: 500;
  line-height: 1.4;
  flex: 1;
  white-space: normal;
  word-break: break-word;
  display: inline-block;
  vertical-align: middle;
}

/* 订单信息表格样式 */
.order-info-table {
  border: 1px solid #e5e7eb;
  border-radius: 2px;
  overflow: hidden;
}

.order-info-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 0;
}

.info-cell {
  display: flex;
  border-bottom: 1px solid #e5e7eb;
  border-right: 1px solid #e5e7eb;
}

.info-cell:nth-child(3n) {
  border-right: none;
}

.info-cell:nth-last-child(-n + 3):nth-child(3n + 1),
.info-cell:nth-last-child(-n + 2):nth-child(3n + 2),
.info-cell:nth-last-child(-n + 1):nth-child(3n) {
  border-bottom: none;
}

.info-label {
  width: 100px;
  padding: 12px 16px;
  background-color: #f5f5f5;
  font-weight: bold;
  text-align: center;
  border-right: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
}

.info-value {
  flex: 1;
  padding: 12px 16px;
  display: flex;
  align-items: center;
}

/* 金额合计样式 */
.amount-summary {
  border: 1px solid #e5e7eb;
  border-radius: 2px;
}

.amount-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
}

.amount-label {
  font-size: 14px;
  color: #333;
}

.amount-value {
  font-size: 16px;
  color: #f53f3f;
  font-weight: bold;
}

/* 商品列表样式 */
.product-list {
  width: 100%;
  margin-top: 10px;
  text-align: left;
}

.product-item {
  padding: 8px 0;
  position: relative;
}

.product-divider {
  height: 1px;
  background-color: #f2f3f5;
  margin: 8px 0;
}

/* 价格列表样式 */
.price-list {
  width: 100%;
}

.price-item {
  padding: 8px 0;
  position: relative;
}

.price-divider {
  height: 1px;
  background-color: #f2f3f5;
  margin: 8px 0;
}

/* 覆盖表格行高亮样式 */
:deep(.arco-table-tr) {
  cursor: default !important;
}

// :deep(.arco-table-tr:hover td) {
//   background-color: var(--color-bg-2) !important;
// }

:deep(.arco-table-tr-selected) {
  background-color: transparent !important;
}

:deep(.arco-table-tr-selected td) {
  background-color: transparent !important;
}

:deep(.arco-table-tr:focus-within) {
  background-color: transparent !important;
  outline: none !important;
  border: none !important;
}

:deep(.arco-table-tr.arco-table-tr-selected) {
  background-color: transparent !important;
}

:deep(.arco-table-tr.arco-table-tr-selected td) {
  background-color: transparent !important;
}

// :deep(.arco-table-tr.arco-table-tr-selected:hover td) {
//   background-color: var(--color-bg-2) !important;
// }

:deep(.arco-table-td) {
  border-right: none !important;
}

::v-deep .arco-table-td {
  background: #ffffff !important;
}

/* 调整表格行高和对齐方式 */
:deep(.arco-table-body .arco-table-tr) {
  height: auto !important;
}

:deep(.arco-table-td) {
  padding-top: 12px !important;
  padding-bottom: 12px !important;
  vertical-align: middle !important;
  text-align: center !important;
}

:deep(.arco-table-th) {
  text-align: center !important;
}

// /* 商品信息和价格列表居中对齐 */
.product-list,
.price-list {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.price-item {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 5px 0;
}

.product-item {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: left;
  padding: 5px 0;
}

.product-content {
  display: flex;
  align-items: flex-start;
  padding: 8px 0;
}

.product-image {
  margin-right: 12px;
  flex-shrink: 0;
}

.product-text {
  flex: 1;
  text-align: left;
}

.order-basic-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-align: left;
  padding: 8px 0;
}

.order-id {
  font-size: 14px;
  color: #1890ff;
  font-weight: 500;
  margin-right: 20px;
}

.order-time,
.order-source {
  font-size: 12px;
  color: #666;
  margin-right: 20px;
}

/* 订单头部行样式 */
.order-header-row {
  background-color: #f5f7fa;
}

.order-header-cell {
  padding: 8px 16px !important;
  border-bottom: 1px solid #e8eaec;
}

.order-header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
}

/* 自定义订单头部行样式 */
.order-header-info {
  position: absolute;
  height: 50px;
  width: calc(100%);
  /* 增加宽度以包含操作栏 */
  margin-bottom: 10px;
  z-index: 1000;
  /* 提高z-index使其显示在操作栏上方 */
  /* 添加以下属性使其占据整个表格宽度 */
  box-sizing: border-box;
  margin-left: -33px;
  margin-top: -20px;
  left: 33px;
  /* 确保右侧能够延伸到操作栏 */
}

.order-header-row-custom {
  background-color: #f5f7f9;
  display: flex;
  align-items: center;
  // margin-top: 10px;

  // z-index: 999999;
  // border: 1px solid #e8e8e8;
  height: 100%;
  border-radius: 4px;
  padding: 8px 12px;
}

.order-header-content-custom {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.order-id-custom {
  font-size: 14px;
  color: #1890ff;
  font-weight: 500;
  margin-right: 16px;
}

.order-time-custom,
.order-source-custom {
  font-size: 12px;
  color: #666;
  margin-right: 16px;
}

.order-header-left {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.order-id-text {
  font-size: 14px;
  color: #1890ff;
  font-weight: 500;
  margin-right: 8px;
}

.order-time-text,
.order-source-text {
  font-size: 12px;
  color: #666;
  margin-right: 16px;
  margin-left: 16px;
}

.copy-btn-inline {
  margin-left: 4px;
  color: #666;
  font-size: 12px;
}

.order-checkbox {
  margin-right: 10px;
}

.add-note-btn {
  color: #1890ff;
  font-size: 12px;
}
</style>
