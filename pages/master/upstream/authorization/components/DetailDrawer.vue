<!--
 - MineAdmin is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -->
<template>
  <!-- 授权详情抽屉 -->
  <a-drawer 
    :width="650" 
    :visible="visible" 
    @cancel="closeDrawer"
    @ok="handleok"
  >
    <template #title>
      授权详情
    </template>
    
    <div class="auth-detail-container">
      <!-- 授权基本信息 -->
      <div class="auth-header mb-4 pb-4 border-b border-gray-200">
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center">
            <a-avatar 
              :size="64" 
              :image-url="record.platformIcon || '/assets/images/platforms/' + record.platformId + '.png'" 
              class="mr-4"
            >
              <template #icon>
                <icon-user />
              </template>
            </a-avatar>
            <div>
              <h3 class="text-lg font-medium">{{ record.name || '未命名店铺' }}</h3>
              <p class="text-gray-500">店铺编码：{{ record.code || '-' }}</p>
            </div>
          </div>
          <div>
            <a-button type="primary" @click="handleEdit">编辑</a-button>
          </div>
        </div>
      </div>
      
      <!-- 授权信息 -->
      <div class="auth-info-section mb-4 pb-4 border-b border-gray-200">
        <h4 class="text-base font-medium mb-2 text-blue-500">基础信息</h4>
        <div class="grid grid-cols-2 gap-4">
          <div class="info-item">
            <div class="text-gray-500">店铺名称：</div>
            <div>{{ record.name || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">店铺代码：</div>
            <div>{{ record.code || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">发票抬头：</div>
            <div>{{ record.invoiceTitle || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">主体名称：</div>
            <div>{{ record.principalName || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">平台名称：</div>
            <div>{{ record.platformName || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">平台ID：</div>
            <div>{{ record.platformId || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">渠道名称：</div>
            <div>{{ record.channelName || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">渠道ID：</div>
            <div>{{ record.channelId || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">店铺状态：</div>
            <div>
              <a-tag :color="record.status == 1 ? 'green' : 'red'">
                {{ record.status == 1 ? '启用' : '禁用' }}
              </a-tag>
            </div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">Cookies：</div>
            <div>{{ record.cookies || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">创建时间：</div>
            <div>{{ formatDate(record.createdAt) || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">更新时间：</div>
            <div>{{ formatDate(record.updatedAt) || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">备注：</div>
            <div>{{ record.remark || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">自动同步：</div>
            <div>
              <a-tag :color="record.isAutoSync ? 'green' : 'gray'">
                {{ record.isAutoSync ? '已开启' : '已关闭' }}
              </a-tag>
            </div>
          </div>
        </div>
        
        <!-- 账号配置信息 -->
        <div v-if="record.accountConfig && Object.keys(record.accountConfig).length > 0" class="mt-4">
          <h4 class="text-base font-medium mb-2 text-blue-500">账号配置</h4>
          <div class="grid grid-cols-2 gap-4">
            <div class="info-item">
              <div class="text-gray-500">用户名：</div>
              <div>{{ record.accountConfig.username || '-' }}</div>
            </div>
            <div class="info-item">
              <div class="text-gray-500">密码：</div>
              <div>{{ record.accountConfig.password ? '******' : '-' }}</div>
            </div>
            <div class="info-item" v-if="record.accountConfig.apiKey">
              <div class="text-gray-500">API密钥：</div>
              <div>{{ record.accountConfig.apiKey.substring(0, 6) + '******' }}</div>
            </div>
          </div>
        </div>
      </div>


      <!-- 信息同步 -->
      <div class="sync-section mb-4 pb-4 border-b border-gray-200">
        <h4 class="text-base font-medium mb-2 text-blue-500">信息同步</h4>
        <div class="flex flex-wrap gap-3">
          <template v-if="record.spiders && record.spiders.length > 0">
            <a-button 
              v-for="spider in record.spiders" 
              :key="spider.spiderType" 
              type="primary" 
              @click="handleSync(spider.spiderType)"
            >
              {{ getSyncTypeName(spider.spiderType) }}同步
            </a-button>
          </template>
          <template v-else>
            <a-button type="primary" @click="handleSync('order')" v-if="hasSpiderType('order')">订单同步</a-button>
            <a-button type="primary" @click="handleSync('goods')" v-if="hasSpiderType('goods')">商品同步</a-button>
            <a-button type="primary" @click="handleSync('invoice')" v-if="hasSpiderType('invoice')">发票同步</a-button>
            <a-button type="primary" @click="handleSync('report')" v-if="hasSpiderType('report')">报备同步</a-button>
          </template>
        </div>
      </div>
      
      <!-- 同步日志 -->
      <div class="sync-log-section">
        <h4 class="text-base font-medium mb-2 text-blue-500">同步日志</h4>
        <div class="flex justify-between items-center mb-2">
          <span class="text-gray-500">共 {{ logTotal }} 条记录</span>
          <a-button type="text" @click="refreshLogs">刷新</a-button>
        </div>
        <a-table 
          :data="syncLogs" 
          :bordered="false" 
          :loading="logsLoading"
          :pagination="{
            total: logTotal,
            current: logPage,
            pageSize: logPageSize,
            showTotal: true,
            showPageSize: true
          }"
          @page-change="handlePageChange"
          @page-size-change="handlePageSizeChange"
          size="small"
        >
          <template #columns>
            <a-table-column title="类型" data-index="spider_type">
              <template #cell="{ record }">
                <a-tag :color="getSyncTypeColor(record.spider_type)">
                  {{ getSyncTypeName(record.spider_type) }}
                </a-tag>
              </template>
            </a-table-column>
            <a-table-column title="数量" data-index="count">
              <template #cell="{ record }">
                {{ record.items_count || record.count || record.sync_count || 0 }}
              </template>
            </a-table-column>
            <a-table-column title="同步时间" data-index="sync_time">
              <template #cell="{ record }">
                {{ formatDate(record.sync_time || record.created_at || record.run_time) }}
              </template>
            </a-table-column>
          </template>
        </a-table>
      </div>
    </div>
  </a-drawer>
</template>

<script setup>
import { ref, defineProps, defineEmits, onMounted, watch } from 'vue';
import { Message } from '@arco-design/web-vue';
import { IconUser } from '@arco-design/web-vue/es/icon';
import upstreamApi from '@/api/master/upstream';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  record: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'edit']);

// 同步日志数据
const syncLogs = ref([]);
const logsLoading = ref(false);
const logPage = ref(1);
const logPageSize = ref(10);
const logTotal = ref(0);

// 获取爬虫日志
const fetchLogs = async () => {
  if (!props.record || !props.record.id) return;
  
  logsLoading.value = true;
  try {
    const params = {
      page: logPage.value,
      pageSize: logPageSize.value,
      storeId: props.record.id
    };
    
    const res = await upstreamApi.spider.getLogs(params);
    console.log('获取爬虫日志:', res);
    
    // 详细输出响应结构，便于调试
    console.log('响应结构:', JSON.stringify(res));
    
    // 适配服务端返回的数据结构
    if (res) {
      // 处理数据部分
      if (Array.isArray(res.data)) {
        // 数据是数组，直接使用
        syncLogs.value = res.data;
      } else if (res.data && typeof res.data === 'object') {
        // 如果数据是对象，尝试获取其中的数组
        const items = res.data.items || res.data.list || res.data.rows || res.data.records;
        if (Array.isArray(items)) {
          syncLogs.value = items;
        } else {
          syncLogs.value = [];
        }
      } else {
        syncLogs.value = [];
      }
      
      // 处理分页信息
      if (res.pagination) {
        // 使用服务端返回的分页信息
        logTotal.value = res.pagination.total || 0;
        logPage.value = res.pagination.page || 1;
        logPageSize.value = res.pagination.pageSize || 10;
      } else {
        // 默认值
        logTotal.value = syncLogs.value.length;
      }
      
      console.log('处理后的日志数据:', syncLogs.value);
    } else {
      syncLogs.value = [];
      logTotal.value = 0;
    }
  } catch (error) {
    console.error('获取爬虫日志失败:', error);
    Message.error('获取爬虫日志失败');
    syncLogs.value = [];
    logTotal.value = 0;
  } finally {
    logsLoading.value = false;
  }
};

// 刷新日志
const refreshLogs = () => {
  fetchLogs();
};

// 页码变化
const handlePageChange = (page) => {
  logPage.value = page;
  fetchLogs();
};

// 每页条数变化
const handlePageSizeChange = (pageSize) => {
  logPageSize.value = pageSize;
  logPage.value = 1; // 重置到第一页
  fetchLogs();
};

// 监听抽屉可见性变化，当抽屉打开时获取日志
watch(() => props.visible, (newVal) => {
  if (newVal && props.record && props.record.id) {
    logPage.value = 1; // 重置页码
    fetchLogs();
  }
});

// 监听记录变化，当记录ID变化时重新获取日志
watch(() => props.record?.id, (newVal) => {
  if (newVal && props.visible) {
    logPage.value = 1; // 重置页码
    fetchLogs();
  }
});

// 关闭抽屉
function closeDrawer() {
  emit('update:visible', false);
}
//
const handleok = () => {
  emit('update:visible', false);
}
// 编辑按钮处理函数
function handleEdit() {
  emit('edit', props.record);
}

// 同步按钮处理函数
async function handleSync(type) {
  let actionName = '';
  switch(type) {
    case 'order':
      actionName = '订单';
      break;
    case 'goods':
      actionName = '商品';
      break;
    case 'invoice':
      actionName = '发票';
      break;
    case 'report':
      actionName = '报备';
      break;
  }

  try {
    // 获取上次同步时间，如果没有则使用24小时前
    const lastSyncTime = getLastSyncTime(type);

    // 构建调用参数
    const callData = {
      platform_id: props.record.platformId?.toString(),
      store_id: props.record.id?.toString(),
      spider_type: type,
      mode: 'incremental',
      last_sync_time: lastSyncTime
    };

    console.log(`开始${actionName}同步，调用参数:`, callData);
    Message.info(`开始${actionName}同步，店铺：${props.record.name}`);

    // 调用爬虫接口
    const response = await upstreamApi.spider.callSpider(callData);
    console.log(`${actionName}同步调用成功:`, response);

    Message.success(`${actionName}同步调用成功！`);

    // 刷新日志列表
    setTimeout(() => {
      refreshLogs();
    }, 1000);

  } catch (error) {
    console.error(`${actionName}同步调用失败:`, error);
    Message.error(`${actionName}同步调用失败: ${error.message || '未知错误'}`);
  }
}

// 获取上次同步时间
function getLastSyncTime(spiderType) {
  // 查找该类型爬虫的最近一次同步时间
  const recentLog = syncLogs.value.find(log =>
    log.spider_type === spiderType
  );

  if (recentLog) {
    // 如果有最近的日志，使用其同步时间
    const syncTime = recentLog.sync_time || recentLog.created_at || recentLog.start_time;
    if (syncTime) {
      const timestamp = new Date(syncTime).getTime();
      if (!isNaN(timestamp)) {
        console.log(`使用上次同步时间: ${syncTime} (${timestamp})`);
        return timestamp;
      }
    }
  }

  // 如果没有找到有效的上次同步时间，使用24小时前
  const defaultTime = Date.now() - (24 * 60 * 60 * 1000);
  console.log(`使用默认同步时间: 24小时前 (${defaultTime})`);
  return defaultTime;
}

// 获取同步类型名称
function getSyncTypeName(type) {
  switch(type) {
    case 'order': return '订单';
    case 'goods': return '商品';
    case 'invoice': return '发票';
    case 'report': return '报备';
    default: return '未知';
  }
}

// 获取同步类型颜色
function getSyncTypeColor(type) {
  switch(type) {
    case 'order': return 'blue';
    case 'goods': return 'green';
    case 'invoice': return 'orange';
    case 'report': return 'purple';
    default: return 'gray';
  }
}

// 检查是否有特定类型的爬虫
function hasSpiderType(type) {
  if (!props.record || !props.record.spiders) return false; // 如果没有spiders数据，不显示按钮
  return props.record.spiders.some(spider => spider.spiderType === type);
}

// 日期格式化函数
function formatDate(dateValue) {
  if (!dateValue) return '-';
  
  try {
    // 处理时间戳（数字或数字字符串）
    let date;
    if (typeof dateValue === 'number' || !isNaN(Number(dateValue))) {
      // 如果是毫秒时间戳
      date = new Date(Number(dateValue));
    } else {
      // 如果是日期字符串
      date = new Date(dateValue);
    }
    
    // 验证日期是否有效
    if (isNaN(date.getTime())) {
      return '-';
    }
    
    // 格式化日期为 YYYY-MM-DD HH:MM:SS
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  } catch (e) {
    return '-';
  }
}
</script>

<style scoped>
/* 详情抽屉样式 */
.info-item {
  display: flex;
  margin-bottom: 8px;
}

.info-item .text-gray-500 {
  width: 100px;
  flex-shrink: 0;
}
</style>
