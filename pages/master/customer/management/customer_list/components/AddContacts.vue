<template>
  <a-drawer
    :visible="visible"
    title="新增/编辑联系人"
    width="800px"
    :footer="false"
    @cancel="handleCancel"
    unmountOnClose
  >
    <div class="add-contacts-form">
      <a-form ref="formRef" :model="formData" :rules="rules" layout="vertical">
        <a-row :gutter="16">
          <!-- 联系人姓名 -->
          <a-col :span="12">
            <a-form-item
              field="contactName"
              label="联系人姓名"
              :rules="[{ required: true, message: '请输入联系人姓名' }]"
            >
              <a-input v-model="formData.contactName" placeholder="请输入" />
            </a-form-item>
          </a-col>

          <!-- 联系电话 -->
          <a-col :span="12">
            <a-form-item
              field="contactPhone"
              label="联系电话"
              :rules="[{ required: true, message: '请输入联系电话' }]"
            >
              <a-input v-model="formData.contactPhone" placeholder="请输入" />
            </a-form-item>
          </a-col>

          <!-- 微信号 -->
          <a-col :span="12">
            <a-form-item field="wechatNumber" label="微信号">
              <a-input v-model="formData.wechatNumber" placeholder="请输入" />
            </a-form-item>
          </a-col>

          <!-- 电子邮箱 -->
          <a-col :span="12">
            <a-form-item field="email" label="电子邮箱">
              <a-input v-model="formData.email" placeholder="请输入" />
            </a-form-item>
          </a-col>

          <!-- 所属部门 -->
          <a-col :span="12">
            <a-form-item field="department" label="所属部门">
              <a-input v-model="formData.department" placeholder="请输入" />
            </a-form-item>
          </a-col>

          <!-- 公司职位 -->
          <a-col :span="12">
            <a-form-item field="position" label="公司职位">
              <a-input v-model="formData.position" placeholder="请输入" />
            </a-form-item>
          </a-col>

          <!-- 生日 -->
          <a-col :span="12">
            <a-form-item field="birthday" label="生日">
              <a-date-picker
                v-model="formData.birthday"
                placeholder="请输入"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>

          <!-- 所在地区 -->
          <a-col :span="12">
            <a-form-item field="location" label="所在地区">
              <a-input v-model="formData.location" placeholder="请输入" />
            </a-form-item>
          </a-col>

          <!-- 详细地址 -->
          <a-col :span="12">
            <a-form-item field="address" label="详细地址">
              <a-input v-model="formData.address" placeholder="请输入" />
            </a-form-item>
          </a-col>

          <!-- 默认联系人 -->
          <a-col :span="12">
            <a-form-item
              field="isDefault"
              label="默认联系人"
              :rules="[{ required: true, message: '请选择是否为默认联系人' }]"
            >
              <a-radio-group v-model="formData.isDefault">
                <a-radio :value="1">是</a-radio>
                <a-radio :value="0">否</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>

          <!-- 备注 -->
          <a-col :span="24">
            <a-form-item field="remark" label="备注">
              <a-textarea
                v-model="formData.remark"
                placeholder="请输入"
                :auto-size="{ minRows: 2, maxRows: 4 }"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 操作按钮 -->
        <div class="form-actions" style="text-align: right; margin-top: 24px">
          <a-space>
            <a-button @click="handleCancel">取消</a-button>
            <a-button
              type="primary"
              @click="handleSubmit"
              :loading="submitLoading"
            >
              确定
            </a-button>
          </a-space>
        </div>
      </a-form>
    </div>
  </a-drawer>
</template>

<script setup>
import { ref, reactive, nextTick } from "vue";
import { Message } from "@arco-design/web-vue";

// 控制抽屉显示
const visible = ref(false);
const submitLoading = ref(false);
const formRef = ref(null);

// 表单数据
const formData = reactive({
  id: null, // 添加id字段，用于区分新增和编辑
  contactName: "",
  contactPhone: "",
  wechatNumber: "",
  email: "",
  department: "",
  position: "",
  birthday: "",
  location: "",
  address: "",
  remark: "",
  isDefault: 0,
});

// 表单验证规则
const rules = {
  contactName: [{ required: true, message: "请输入联系人姓名" }],
  contactPhone: [
    { required: true, message: "请输入联系电话" },
    {
      pattern: /^1[3-9]\d{9}$/,
      message: "请输入正确的手机号码",
    },
  ],
  email: [
    {
      type: "email",
      message: "请输入正确的邮箱地址",
    },
  ],
  isDefault: [{ required: true, message: "请选择是否为默认联系人" }],
};

// 事件
const emit = defineEmits(["success"]);

// 打开抽屉
const open = (data = {}) => {
  console.log("打开联系人抽屉，传入数据:", data);

  // 如果传入数据，则填充表单（编辑模式）
  if (Object.keys(data).length > 0) {
    Object.keys(formData).forEach((key) => {
      if (data[key] !== undefined) {
        formData[key] = data[key];
      }
    });
    console.log("编辑模式，表单数据:", formData);
  } else {
    // 新增模式，重置表单
    resetForm();
    console.log("新增模式，表单已重置");
  }

  visible.value = true;

  // 等待DOM更新后清除验证状态
  nextTick(() => {
    if (formRef.value) {
      formRef.value.clearValidate();
    }
  });
};

// 关闭抽屉
const handleCancel = () => {
  visible.value = false;
  resetForm();
};

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach((key) => {
    if (key === "isDefault") {
      formData[key] = 0;
    } else if (key === "id") {
      formData[key] = null;
    } else {
      formData[key] = "";
    }
  });

  if (formRef.value) {
    formRef.value.clearValidate();
  }
};

// 提交表单
const handleSubmit = async () => {
  try {
    // 验证表单
    const valid = await formRef.value.validate();
    if (valid) {
      return;
    }

    submitLoading.value = true;

    // 这里应该调用API保存数据
    // const result = await saveContact(formData)

    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 1000));

    Message.success("保存成功");
    // 传递表单数据给父组件
    console.log("提交表单数据:", formData);
    emit("success", { ...formData });
    handleCancel();
  } catch (error) {
    console.error("保存失败:", error);
    Message.error("保存失败，请重试");
  } finally {
    submitLoading.value = false;
  }
};

// 暴露方法给父组件
defineExpose({
  open,
});
</script>

<style scoped>
.add-contacts-form {
  padding: 0;
}

.form-actions {
  border-top: 1px solid #e5e7eb;
  padding-top: 16px;
  margin-top: 24px;
}
</style>
