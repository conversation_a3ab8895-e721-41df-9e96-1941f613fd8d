<template>
  <a-drawer
    v-model:visible="visible"
    title="新增/编辑发票抬头"
    width="500px"
    :footer="false"
    placement="right"
    @cancel="handleCancel"
  >
    <div class="invoice-title-form">
      <a-form
        ref="formRef"
        :model="formData"
        layout="vertical"
        :rules="formRules"
      >
        <a-form-item
          field="invoiceTitle"
          label="发票抬头"
          :rules="[{ required: true, message: '请输入发票抬头' }]"
        >
          <a-input
            v-model="formData.invoiceTitle"
            placeholder="请输入"
            allow-clear
          />
        </a-form-item>

        <a-form-item
          field="taxNumber"
          label="税号"
          :rules="[{ required: true, message: '请输入税号' }]"
        >
          <a-input
            v-model="formData.taxNumber"
            placeholder="请输入"
            allow-clear
          />
        </a-form-item>

        <a-form-item
          field="companyAddress"
          label="单位地址"
          :rules="[{ required: true, message: '请输入单位地址' }]"
        >
          <a-input
            v-model="formData.companyAddress"
            placeholder="请输入"
            allow-clear
          />
        </a-form-item>

        <a-form-item
          field="companyPhone"
          label="公司电话"
          :rules="[{ required: true, message: '请输入公司电话' }]"
        >
          <a-input
            v-model="formData.companyPhone"
            placeholder="请输入"
            allow-clear
          />
        </a-form-item>

        <a-form-item
          field="bankName"
          label="开户银行"
          :rules="[{ required: true, message: '请输入开户银行' }]"
        >
          <a-input
            v-model="formData.bankName"
            placeholder="请输入"
            allow-clear
          />
        </a-form-item>

        <a-form-item
          field="bankAccount"
          label="银行账户"
          :rules="[{ required: true, message: '请输入银行账户' }]"
        >
          <a-input
            v-model="formData.bankAccount"
            placeholder="请输入"
            allow-clear
          />
        </a-form-item>
      </a-form>

      <!-- 底部操作按钮 -->
      <div class="footer-actions">
        <a-space>
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" @click="handleConfirm" :loading="loading">
            确定
          </a-button>
        </a-space>
      </div>
    </div>
  </a-drawer>
</template>

<script setup>
import { ref, reactive, watch } from "vue";
import { Message } from "@arco-design/web-vue";

// 定义组件属性
const props = defineProps({
  // 编辑模式下传入的数据
  editData: {
    type: Object,
    default: () => ({}),
  },
});

// 定义事件
const emit = defineEmits(["success", "cancel"]);

// 响应式数据
const visible = ref(false);
const loading = ref(false);
const formRef = ref();

// 表单数据
const formData = reactive({
  invoiceTitle: "",
  taxNumber: "",
  companyAddress: "",
  companyPhone: "",
  bankName: "",
  bankAccount: "",
});

// 表单验证规则
const formRules = {
  invoiceTitle: [{ required: true, message: "请输入发票抬头" }],
  taxNumber: [{ required: true, message: "请输入税号" }],
  companyAddress: [{ required: true, message: "请输入单位地址" }],
  companyPhone: [{ required: true, message: "请输入公司电话" }],
  bankName: [{ required: true, message: "请输入开户银行" }],
  bankAccount: [{ required: true, message: "请输入银行账户" }],
};

// 监听编辑数据变化
watch(
  () => props.editData,
  (newData) => {
    if (newData && Object.keys(newData).length > 0) {
      Object.assign(formData, {
        invoiceTitle: newData.invoiceTitle || "",
        taxNumber: newData.taxNumber || "",
        companyAddress: newData.companyAddress || "",
        companyPhone: newData.companyPhone || "",
        bankName: newData.bankName || "",
        bankAccount: newData.bankAccount || "",
      });
    }
  },
  { immediate: true }
);

// 打开抽屉
const open = (editData = null) => {
  visible.value = true;

  if (editData) {
    // 编辑模式，填充数据
    Object.assign(formData, {
      invoiceTitle: editData.invoiceTitle || "",
      taxNumber: editData.taxNumber || "",
      companyAddress: editData.companyAddress || "",
      companyPhone: editData.companyPhone || "",
      bankName: editData.bankName || "",
      bankAccount: editData.bankAccount || "",
    });
  } else {
    // 新增模式，重置表单
    resetForm();
  }
};

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    invoiceTitle: "",
    taxNumber: "",
    companyAddress: "",
    companyPhone: "",
    bankName: "",
    bankAccount: "",
  });

  // 清除表单验证状态
  if (formRef.value) {
    formRef.value.clearValidate();
  }
};

// 取消操作
const handleCancel = () => {
  visible.value = false;
  resetForm();
  emit("cancel");
};

// 确定操作
const handleConfirm = async () => {
  try {
    // 表单验证
    const valid = await formRef.value.validate();
    console.log("valid: ", valid);
    if (valid) {
      return;
    }

    loading.value = true;

    // 这里应该调用API保存数据
    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // 构造返回数据
    const resultData = {
      ...formData,
      id: Date.now(), // 模拟ID
      createTime: new Date().toISOString(),
    };

    Message.success("保存成功");
    visible.value = false;
    resetForm();
    emit("success", resultData);
  } catch (error) {
    console.error("保存发票抬头失败:", error);
    Message.error("保存失败，请稍后重试");
  } finally {
    loading.value = false;
  }
};

// 暴露方法给父组件
defineExpose({
  open,
});
</script>
<style scoped>
.invoice-title-form {
  padding: 0;
}

.footer-actions {
  position: sticky;
  bottom: 0;
  background: #fff;
  padding: 16px 0;
  border-top: 1px solid #e5e7eb;
  margin-top: 24px;
  display: flex;
  justify-content: flex-end;
}

/* 表单项样式优化 */
:deep(.arco-form-item) {
  margin-bottom: 20px;
}

:deep(.arco-form-item-label) {
  font-weight: 500;
  color: #374151;
}

:deep(.arco-input) {
  border-radius: 6px;
}

:deep(.arco-input:focus) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

/* 抽屉标题样式 */
:deep(.arco-drawer-header) {
  border-bottom: 1px solid #e5e7eb;
  padding: 16px 24px;
}

:deep(.arco-drawer-title) {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
}

/* 抽屉内容区域 */
:deep(.arco-drawer-body) {
  padding: 24px;
}
</style>
