<template>
  <div>
    <a-card title="基础资料" class="mb-6">
      <a-form :model="formData">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item
              field="customerName"
              label="客户名称"
              label-col-flex="100px"
              required
            >
              <a-input
                v-model="formData.customerName"
                placeholder="请输入客户名称"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item
              field="customerShortName"
              label="客户简称"
              label-col-flex="100px"
            >
              <a-input
                v-model="formData.customerShortName"
                placeholder="请输入客户简称"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="group" label="所属集团" label-col-flex="100px">
              <a-input
                v-model="formData.group"
                placeholder="请输入所属集团"
                allow-clear
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item
              field="customerSource"
              label="客户来源"
              label-col-flex="100px"
            >
              <a-select
                v-model="formData.customerSource"
                placeholder="请选择客户来源"
                allow-clear
              >
                <a-option value="主动获取">主动获取</a-option>
                <a-option value="推荐介绍">推荐介绍</a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item
              field="industry"
              label="所属行业"
              label-col-flex="100px"
              required
            >
              <a-select
                v-model="formData.industry"
                placeholder="请选择所属行业"
                allow-clear
              >
                <a-option value="互联网">互联网</a-option>
                <a-option value="金融">金融</a-option>
                <a-option value="教育">教育</a-option>
                <a-option value="医疗">医疗</a-option>
                <a-option value="制造业">制造业</a-option>
                <a-option value="服务业">服务业</a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item
              field="customerStatus"
              label="客户状态"
              label-col-flex="100px"
              required
            >
              <a-select
                v-model="formData.customerStatus"
                placeholder="请选择客户状态"
                allow-clear
              >
                <a-option value="在营">在营</a-option>
                <a-option value="暂停营业">暂停营业</a-option>
                <a-option value="关闭">关闭</a-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item
              field="companyScale"
              label="企业规模"
              label-col-flex="100px"
            >
              <a-select
                v-model="formData.companyScale"
                placeholder="请选择企业规模"
                allow-clear
              >
                <a-option value="初创企业">初创企业</a-option>
                <a-option value="中小企业">中小企业</a-option>
                <a-option value="中型企业">中型企业</a-option>
                <a-option value="大型企业">大型企业</a-option>
                <a-option value="集团企业">集团企业</a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item
              field="companyRegion"
              label="公司地区"
              label-col-flex="100px"
            >
              <a-input
                v-model="formData.companyRegion"
                placeholder="请输入公司地区"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item
              field="detailAddress"
              label="详细地址"
              label-col-flex="100px"
            >
              <a-input
                v-model="formData.detailAddress"
                placeholder="请输入详细地址"
                allow-clear
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item
              field="customerRemark"
              label="客户备注"
              label-col-flex="100px"
            >
              <a-textarea
                v-model="formData.customerRemark"
                placeholder="请输入客户备注"
                :rows="3"
                allow-clear
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 附件上传区域 -->
        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item
              field="attachments"
              label="附件上传"
              label-col-flex="100px"
            >
              <a-upload
                :file-list="fileList"
                :show-file-list="true"
                :auto-upload="false"
                @change="handleFileChange"
                @remove="handleFileRemove"
                multiple
              >
                <template #upload-button>
                  <div
                    class="flex items-center justify-center w-32 h-20 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-400 cursor-pointer"
                  >
                    <div class="text-center">
                      <icon-upload class="text-2xl text-gray-400 mb-1" />
                      <div class="text-sm text-gray-500">上传</div>
                    </div>
                  </div>
                </template>
              </a-upload>
              <div class="mt-2 text-xs text-gray-500">
                支持上传 pdf/png/jpg/jpeg/doc/docx 等格式文件，单个文件不超过
                20MB
              </div>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-card>

    <!-- 财务信息 -->
    <a-card title="财务信息" class="mb-6">
      <a-form :model="financeData">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item
              field="accountHolder"
              label="开户人名称"
              label-col-flex="100px"
            >
              <a-input
                v-model="financeData.accountHolder"
                placeholder="请输入"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="bankName" label="开户行" label-col-flex="100px">
              <a-input
                v-model="financeData.bankName"
                placeholder="请输入"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item
              field="bankAccount"
              label="银行账号"
              label-col-flex="100px"
            >
              <a-input
                v-model="financeData.bankAccount"
                placeholder="请输入"
                allow-clear
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item
              field="accountPeriodBasis"
              label="账期依据"
              label-col-flex="100px"
            >
              <a-select
                v-model="financeData.accountPeriodBasis"
                placeholder="请选择账期依据"
                allow-clear
              >
                <a-option value="订单已发货">订单已发货</a-option>
                <a-option value="订单已收货">订单已收货</a-option>
                <a-option value="订单已开票">订单已开票</a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item
              field="settlementMethod"
              label="结算方式"
              label-col-flex="100px"
            >
              <a-select
                v-model="financeData.settlementMethod"
                placeholder="请选择结算方式"
                allow-clear
              >
                <a-option value="月结">月结</a-option>
                <a-option value="固定账期">固定账期</a-option>
                <a-option value="现销现结">现销现结</a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item
              field="settlementDate"
              label="结算日期"
              label-col-flex="100px"
            >
              <a-input
                v-model="financeData.settlementDate"
                placeholder="请输入"
                allow-clear
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 开票信息 -->
        <a-divider orientation="left">开票信息</a-divider>
        <a-row :gutter="16">
          <a-col :span="24">
            <div class="invoice-info-container">
              <!-- 动态渲染发票抬头列表 -->
              <div
                v-for="invoice in invoiceList"
                :key="invoice.id"
                class="invoice-card"
              >
                <div class="invoice-header">
                  <span class="company-name">{{ invoice.invoiceTitle }}</span>
                  <div class="action-buttons">
                    <a-button
                      type="text"
                      size="small"
                      @click="handleEditInvoice(invoice)"
                    >
                      <icon-edit />
                    </a-button>
                    <a-button
                      type="text"
                      size="small"
                      status="danger"
                      @click="handleDeleteInvoice(invoice.id)"
                    >
                      <icon-delete />
                    </a-button>
                  </div>
                </div>
                <div class="invoice-content">
                  <div class="invoice-row">
                    <span class="label">税号：</span>
                    <span class="value">{{ invoice.taxNumber || "-" }}</span>
                  </div>
                  <div class="invoice-row">
                    <span class="label">单位地址：</span>
                    <span class="value">{{
                      invoice.companyAddress || "-"
                    }}</span>
                  </div>
                  <div class="invoice-row">
                    <span class="label">公司电话：</span>
                    <span class="value">{{ invoice.companyPhone || "-" }}</span>
                  </div>
                  <div class="invoice-row">
                    <span class="label">开户银行：</span>
                    <span class="value">{{ invoice.bankName || "-" }}</span>
                  </div>
                  <div class="invoice-row">
                    <span class="label">银行账户：</span>
                    <span class="value">{{ invoice.bankAccount || "-" }}</span>
                  </div>
                </div>
              </div>

              <!-- 添加发票抬头按钮 -->
              <div class="add-invoice-card">
                <a-button
                  type="dashed"
                  class="add-invoice-btn"
                  style="height: 100%"
                  @click="handleAddInvoice"
                >
                  <icon-plus />
                  添加开票信息
                </a-button>
              </div>
            </div>
          </a-col>
        </a-row>
      </a-form>
    </a-card>

    <!-- 联系信息 -->
    <a-card title="联系信息" class="mb-6">
      <div class="contact-info-container">
        <!-- 动态渲染联系人列表 -->
        <div
          v-for="contact in contactList"
          :key="contact.id"
          class="contact-card"
        >
          <div class="contact-header">
            <span class="contact-name">{{ contact.contactName }}</span>
            <span v-if="contact.position" class="contact-role">{{
              contact.position
            }}</span>
            <span v-if="contact.isDefault === 1" class="default-badge"
              >默认</span
            >
            <div class="action-buttons">
              <a-button
                type="text"
                size="small"
                @click="handleEditContact(contact)"
              >
                <icon-edit />
              </a-button>
              <a-button
                type="text"
                size="small"
                status="danger"
                @click="handleDeleteContact(contact.id)"
              >
                <icon-delete />
              </a-button>
            </div>
          </div>
          <div class="contact-content">
            <div class="contact-phone">{{ contact.contactPhone }}</div>
            <div v-if="contact.remark" class="contact-note">
              备注：{{ contact.remark }}
            </div>
          </div>
        </div>

        <!-- 添加联系人按钮 -->
        <div class="add-contact-card">
          <a-button
            type="dashed"
            class="add-contact-btn"
            style="height: 100%"
            @click="handleAddContact"
          >
            <icon-plus />
            添加联系人
          </a-button>
        </div>
      </div>
    </a-card>

    <!-- 跟进信息 -->
    <a-card title="跟进信息" class="mb-6">
      <a-form :model="followUpData">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item
              field="salesperson1"
              label="业务员"
              label-col-flex="100px"
            >
              <a-select
                v-model="followUpData.salesperson1"
                placeholder="请选择业务员"
                allow-clear
              >
                <a-option value="业务员1">业务员1</a-option>
                <a-option value="业务员2">业务员2</a-option>
                <a-option value="业务员3">业务员3</a-option>
                <a-option value="业务员4">业务员4</a-option>
                <a-option value="业务员5">业务员5</a-option>
                <a-option value="业务员6">业务员6</a-option>
                <a-option value="业务员7">业务员7</a-option>
                <a-option value="业务员8">业务员8</a-option>
                <a-option value="业务员9">业务员9</a-option>
                <a-option value="业务员10">业务员10</a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item
              field="salesperson2"
              label="业务员2"
              label-col-flex="100px"
            >
              <a-select
                v-model="followUpData.salesperson2"
                placeholder="请选择业务员2"
                allow-clear
              >
                <a-option value="业务员1">业务员1</a-option>
                <a-option value="业务员2">业务员2</a-option>
                <a-option value="业务员3">业务员3</a-option>
                <a-option value="业务员4">业务员4</a-option>
                <a-option value="业务员5">业务员5</a-option>
                <a-option value="业务员6">业务员6</a-option>
                <a-option value="业务员7">业务员7</a-option>
                <a-option value="业务员8">业务员8</a-option>
                <a-option value="业务员9">业务员9</a-option>
                <a-option value="业务员10">业务员10</a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item
              field="salesperson3"
              label="业务员3"
              label-col-flex="100px"
            >
              <a-select
                v-model="followUpData.salesperson3"
                placeholder="请选择业务员3"
                allow-clear
              >
                <a-option value="业务员1">业务员1</a-option>
                <a-option value="业务员2">业务员2</a-option>
                <a-option value="业务员3">业务员3</a-option>
                <a-option value="业务员4">业务员4</a-option>
                <a-option value="业务员5">业务员5</a-option>
                <a-option value="业务员6">业务员6</a-option>
                <a-option value="业务员7">业务员7</a-option>
                <a-option value="业务员8">业务员8</a-option>
                <a-option value="业务员9">业务员9</a-option>
                <a-option value="业务员10">业务员10</a-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-card>

    <!-- 操作按钮 -->
    <a-row>
      <a-col :span="24" class="text-center mb-6">
        <a-space>
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" @click="handleSave">保存</a-button>
        </a-space>
      </a-col>
    </a-row>

    <!-- 发票抬头组件 -->
    <AddInvoiceTitle
      ref="addInvoiceTitleRef"
      @success="handleInvoiceSuccess"
      @cancel="handleInvoiceCancel"
    />

    <!-- 联系人组件 -->
    <AddContacts ref="addContactsRef" @success="handleContactSuccess" />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { Message } from "@arco-design/web-vue";
import AddInvoiceTitle from "./components/AddInvoiceTitle.vue";
import AddContacts from "./components/AddContacts.vue";

// 页面元数据
definePageMeta({
  name: "master-customer_add",
  path: "/master/customer/management/customer_list/:id",
});

// 路由相关
const route = useRoute();
const router = useRouter();

// 表单数据
const formData = reactive({
  customerName: "",
  customerShortName: "",
  group: "",
  customerSource: "",
  industry: "",
  customerStatus: "",
  companyScale: "",
  companyRegion: "",
  detailAddress: "",
  customerRemark: "",
  attachments: [],
});

// 财务信息数据
const financeData = reactive({
  accountHolder: "",
  bankName: "",
  bankAccount: "",
  accountPeriodBasis: "",
  settlementMethod: "",
  settlementDate: "",
});

// 跟进信息数据
const followUpData = reactive({
  salesperson1: "",
  salesperson2: "",
  salesperson3: "",
});

// 文件列表
const fileList = ref([]);

// 发票抬头组件引用
const addInvoiceTitleRef = ref();

// 联系人组件引用
const addContactsRef = ref();

// 发票抬头数据
const invoiceList = ref([
  {
    id: 1,
    invoiceTitle: "国际科技大学",
    taxNumber: "12430004446824460W",
    companyAddress: "湖南省长沙市雨花区某105号",
    companyPhone: "",
    bankName: "工商银行长沙市雨花支行",
    bankAccount: "****************.34",
  },
]);

// 联系人数据
const contactList = ref([
  {
    id: 1,
    contactName: "李强东",
    contactPhone: "134****0987",
    wechatNumber: "",
    email: "",
    department: "",
    position: "供应链负责人",
    birthday: "",
    location: "",
    address: "",
    remark: "采购问题联系无先生此人处理",
    isDefault: 1,
  },
  {
    id: 2,
    contactName: "李强东",
    contactPhone: "134****0987",
    wechatNumber: "",
    email: "",
    department: "",
    position: "供应链负责人",
    birthday: "",
    location: "",
    address: "",
    remark: "采购问题联系无先生此人处理",
    isDefault: 0,
  },
]);

// 页面加载时的逻辑
onMounted(() => {
  const customerId = route.params.id;
  if (customerId && customerId !== "add") {
    // 编辑模式，加载客户数据
    loadCustomerData(customerId);
  }
  // 如果是 'add'，则为新增模式，使用空表单
});

// 加载客户数据
const loadCustomerData = async (customerId) => {
  try {
    // 这里应该调用API获取客户数据
    // const response = await api.getCustomer(customerId)
    // Object.assign(formData, response.data)

    // 模拟数据加载
    console.log("加载客户数据:", customerId);
  } catch (error) {
    console.error("加载客户数据失败:", error);
    Message.error("加载客户数据失败");
  }
};

// 文件上传处理
const handleFileChange = (fileList, fileItem) => {
  console.log("文件变化:", fileList, fileItem);
};

// 文件删除处理
const handleFileRemove = (fileItem) => {
  console.log("删除文件:", fileItem);
};

// 保存处理
const handleSave = async () => {
  try {
    // 表单验证
    if (!formData.customerName) {
      Message.error("请输入客户名称");
      return;
    }
    if (!formData.industry) {
      Message.error("请选择所属行业");
      return;
    }
    if (!formData.customerStatus) {
      Message.error("请选择客户状态");
      return;
    }

    // 这里应该调用API保存数据
    // const response = await api.saveCustomer(formData)

    Message.success("保存成功");
    // 保存成功后跳转到列表页
    router.push("/master/customer/management/customer_list");
  } catch (error) {
    console.error("保存失败:", error);
    Message.error("保存失败");
  }
};

// 取消处理
const handleCancel = () => {
  router.back();
};

// 添加发票抬头
const handleAddInvoice = () => {
  addInvoiceTitleRef.value.open();
};

// 编辑发票抬头
const handleEditInvoice = (invoice) => {
  addInvoiceTitleRef.value.open(invoice);
};

// 删除发票抬头
const handleDeleteInvoice = (invoiceId) => {
  const index = invoiceList.value.findIndex((item) => item.id === invoiceId);
  if (index >= 0) {
    invoiceList.value.splice(index, 1);
    Message.success("删除成功");
  }
};

// 发票抬头保存成功回调
const handleInvoiceSuccess = (data) => {
  console.log("发票抬头保存成功:", data);

  // 查找是否已存在（编辑模式）
  const existingIndex = invoiceList.value.findIndex(
    (item) => item.id === data.id
  );

  if (existingIndex >= 0) {
    // 更新现有项
    invoiceList.value[existingIndex] = data;
    Message.success("发票抬头更新成功");
  } else {
    // 添加新项
    invoiceList.value.push(data);
    Message.success("发票抬头添加成功");
  }
};

// 发票抬头取消回调
const handleInvoiceCancel = () => {
  console.log("用户取消发票抬头操作");
};

// 添加联系人
const handleAddContact = () => {
  addContactsRef.value.open();
};

// 编辑联系人
const handleEditContact = (contact) => {
  addContactsRef.value.open(contact);
};

// 删除联系人
const handleDeleteContact = (contactId) => {
  const index = contactList.value.findIndex((item) => item.id === contactId);
  if (index >= 0) {
    contactList.value.splice(index, 1);
    Message.success("删除成功");
  }
};

// 联系人保存成功回调
const handleContactSuccess = (data) => {
  console.log("联系人保存成功:", data);

  // 检查是否有ID（编辑模式）
  if (data.id) {
    // 查找是否已存在（编辑模式）
    const existingIndex = contactList.value.findIndex(
      (item) => item.id === data.id
    );

    if (existingIndex >= 0) {
      // 更新现有项
      contactList.value[existingIndex] = data;
      Message.success("联系人更新成功");
    } else {
      // 如果有ID但找不到，说明是新增，添加到列表
      contactList.value.push(data);
      Message.success("联系人添加成功");
    }
  } else {
    // 新增模式，生成新的ID
    const newContact = {
      ...data,
      id: Date.now(), // 简单的ID生成方式
    };
    contactList.value.push(newContact);
    Message.success("联系人添加成功");
  }
};
</script>

<style scoped>
.invoice-info-container {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.invoice-card {
  background: #f8f9fa;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  width: calc(33.333% - 11px);
  min-width: 280px;
}

.invoice-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.company-name {
  font-weight: 600;
  font-size: 16px;
  color: #1f2937;
}

.action-buttons {
  display: flex;
  gap: 4px;
}

.invoice-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.invoice-row {
  display: flex;
  align-items: center;
  font-size: 14px;
}

.invoice-row .label {
  color: #6b7280;
  min-width: 80px;
  flex-shrink: 0;
}

.invoice-row .value {
  color: #1f2937;
  flex: 1;
}

.add-invoice-card {
  display: flex;
  align-items: stretch;
  justify-content: center;
  width: calc(33.333% - 11px);
  min-width: 280px;
}

.add-invoice-btn {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  background: #fafafa;
  transition: all 0.3s ease;
  padding: 16px;
}

.add-invoice-btn:hover {
  border-color: #3b82f6;
  background: #f0f9ff;
  color: #3b82f6;
}

/* 联系信息样式 */
.contact-info-container {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.contact-card {
  background: #f8f9fa;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  width: calc(33.333% - 11px);
  min-width: 280px;
}

.contact-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  position: relative;
}

.contact-name {
  font-weight: 600;
  font-size: 16px;
  color: #1f2937;
}

.contact-role {
  background: #e5e7eb;
  color: #6b7280;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.default-badge {
  background: #3b82f6;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  margin-left: 8px;
}

.contact-header .action-buttons {
  position: absolute;
  right: 0;
  top: 0;
  display: flex;
  gap: 4px;
}

.contact-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.contact-phone {
  font-size: 14px;
  color: #1f2937;
  font-weight: 500;
}

.contact-note {
  font-size: 12px;
  color: #6b7280;
  line-height: 1.4;
}

.add-contact-card {
  display: flex;
  align-items: stretch;
  justify-content: center;
  width: calc(33.333% - 11px);
  min-width: 280px;
}

.add-contact-btn {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  background: #fafafa;
  transition: all 0.3s ease;
  padding: 16px;
}

.add-contact-btn:hover {
  border-color: #3b82f6;
  background: #f0f9ff;
  color: #3b82f6;
}
</style>
