<template>
  <div class="main-products">
    <ma-crud :options="crudOptions" :columns="columns" ref="crudRef">
      <!-- 更新时间 -->
      <template #updated_at="{ record }">
        <div v-if="record.updated_at" v-time="record.updated_at"></div>
        <div v-else>-</div>
      </template>
    </ma-crud>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { Message } from "@arco-design/web-vue";
import { mainProductApi } from "@/api/master/csm";

const crudRef = ref();

// 真实API函数
const realApi = {
  // 获取列表
  getList: async (params = {}) => {
    try {
      console.log('主营产品搜索参数:', params);
      const result = await mainProductApi.getList(params);
      return result;
    } catch (error) {
      console.error('获取主营产品列表失败:', error);
      Message.error('获取主营产品列表失败');
      throw error;
    }
  },

  // 创建
  create: async (data) => {
    try {
      console.log('创建产品:', data);
      const result = await mainProductApi.create(data);
      Message.success('创建成功');
      return result;
    } catch (error) {
      console.error('创建主营产品失败:', error);
      Message.error('创建主营产品失败');
      throw error;
    }
  },

  // 更新
  update: async (id, data) => {
    try {
      console.log('更新产品:', id, data);
      const result = await mainProductApi.update(id, data);
      Message.success('更新成功');
      return result;
    } catch (error) {
      console.error('更新主营产品失败:', error);
      Message.error('更新主营产品失败');
      throw error;
    }
  },

  // 删除
  delete: async (id) => {
    try {
      console.log('删除产品:', id);
      const result = await mainProductApi.delete(id);
      Message.success('删除成功');
      return result;
    } catch (error) {
      console.error('删除主营产品失败:', error);
      Message.error('删除主营产品失败');
      throw error;
    }
  }
};



// ma-crud 配置项
const crudOptions = reactive({
  api: realApi.getList,
  title: "主营产品管理",
  searchLabelWidth: "100px",
  rowSelection: { showCheckedAll: true },
  operationColumn: true,
  actionColumn: {
    width: 180,
  },
  formOption: { 
    width: 800,
    viewType: 'modal'
  },
  table: {
    rowKey: "id",
    border: true,
    size: "medium",
    pagination: {
      showTotal: true,
      showJumper: true,
      showPageSize: true,
    },
  },
  search: {
    collapsed: false,
    showCollapse: true,
    collapsePosition: "left",
  },
  // 搜索前处理参数
  beforeSearch: (params) => {
    return params;
  },
  // 添加前处理参数
  beforeAdd: (params) => {
    return params;
  },
  // 编辑前处理参数
  beforeEdit: (params) => {
    return params;
  },
  add: {
    show: true,
    api: realApi.create,
    text: "新增产品"
  },
  edit: {
    show: true,
    api: realApi.update,
  },
  delete: {
    show: true,
    api: realApi.delete,
  },
});

// 表格列配置
const columns = reactive([
  {
    title: "序号",
    dataIndex: "id",
    width: 80,
    align: "center",
    search: false,
    addDisplay: false,
    editDisplay: false,
  },
  {
    title: "主营产品名称",
    dataIndex: "product_name",
    width: 200,
    search: true,
    formType: "input",
    commonRules: [{ required: true, message: '主营产品名称必填' }],
  },
  {
    title: "提交人",
    dataIndex: "submitter",
    width: 120,
    search: true,
    formType: "input",
    commonRules: [{ required: true, message: '提交人必填' }],
  },
  {
    title: "提交日期",
    dataIndex: "submit_date",
    width: 120,
    search: false,
    formType: "date",
    commonRules: [{ required: true, message: '提交日期必填' }],
  },
  {
    title: "备注",
    dataIndex: "remark",
    width: 250,
    search: false,
    formType: "textarea",
    rows: 3,
  },
  {
    title: "更新时间",
    dataIndex: "updated_at",
    width: 160,
    search: false,
    addDisplay: false,
    editDisplay: false,
  },
  {
    title: "更新人",
    dataIndex: "updater",
    width: 120,
    search: false,
    formType: "input",
    commonRules: [{ required: true, message: '更新人必填' }],
  },
]);

// 生命周期钩子
onMounted(() => {
  console.log('主营产品管理页面已加载');
});
</script>

<style scoped>
.main-products {
  height: 100%;
}
</style>
