<template>
  <div class="cooperation-agreement">
    <ma-crud :options="crudOptions" :columns="columns" ref="crudRef">
      <!-- 更新时间 -->
      <template #updated_at="{ record }">
        <div v-if="record.updated_at" v-time="record.updated_at"></div>
        <div v-else>-</div>
      </template>
    </ma-crud>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { Message } from "@arco-design/web-vue";
import { cooperationAgreementApi } from "@/api/master/csm";

const crudRef = ref();

// 模拟合作协议数据
const mockAgreements = [
  {
    id: 1,
    agreement_name: "长期供货合作协议",
    submitter: "张三",
    submit_date: "2024-01-15",
    remark: "电子产品长期供货合作协议",
    updated_at: "2024-01-20 14:20:00",
    updater: "李四"
  },
  {
    id: 2,
    agreement_name: "技术服务协议",
    submitter: "李四",
    submit_date: "2024-01-10",
    remark: "软件开发技术服务协议",
    updated_at: "2024-01-18 16:45:00",
    updater: "王五"
  },
  {
    id: 3,
    agreement_name: "保密协议",
    submitter: "王五",
    submit_date: "2024-01-05",
    remark: "商业保密协议",
    updated_at: "2024-01-15 13:30:00",
    updater: "张三"
  },
  {
    id: 4,
    agreement_name: "设备租赁协议",
    submitter: "赵六",
    submit_date: "2023-12-20",
    remark: "生产设备租赁服务协议",
    updated_at: "2024-01-12 10:15:00",
    updater: "李四"
  },
  {
    id: 5,
    agreement_name: "原材料采购框架协议",
    submitter: "孙七",
    submit_date: "2024-01-08",
    remark: "电子元器件采购框架协议",
    updated_at: "2024-01-22 17:20:00",
    updater: "张三"
  }
];

// 真实API函数
const realApi = {
  // 获取列表
  getList: async (params = {}) => {
    try {
      console.log('合作协议搜索参数:', params);
      const result = await cooperationAgreementApi.getList(params);
      return result;
    } catch (error) {
      console.error('获取合作协议列表失败:', error);
      Message.error('获取合作协议列表失败');
      throw error;
    }
  },

  // 创建
  create: async (data) => {
    try {
      console.log('创建合作协议:', data);
      const result = await cooperationAgreementApi.create(data);
      Message.success('创建成功');
      return result;
    } catch (error) {
      console.error('创建合作协议失败:', error);
      Message.error('创建合作协议失败');
      throw error;
    }
  },

  // 更新
  update: async (id, data) => {
    try {
      console.log('更新合作协议:', id, data);
      const result = await cooperationAgreementApi.update(id, data);
      Message.success('更新成功');
      return result;
    } catch (error) {
      console.error('更新合作协议失败:', error);
      Message.error('更新合作协议失败');
      throw error;
    }
  },

  // 删除
  delete: async (id) => {
    try {
      console.log('删除合作协议:', id);
      const result = await cooperationAgreementApi.delete(id);
      Message.success('删除成功');
      return result;
    } catch (error) {
      console.error('删除合作协议失败:', error);
      Message.error('删除合作协议失败');
      throw error;
    }
  }
};

// 模拟API函数（备用）
const mockApi = {
  // 获取列表
  getList: async (params = {}) => {
    console.log('合作协议搜索参数:', params);

    // 模拟延迟
    await new Promise(resolve => setTimeout(resolve, 500));

    let filteredData = [...mockAgreements];
    
    // 模拟搜索过滤
    if (params.agreement_name) {
      filteredData = filteredData.filter(item =>
        item.agreement_name.includes(params.agreement_name)
      );
    }

    if (params.submitter) {
      filteredData = filteredData.filter(item =>
        item.submitter.includes(params.submitter)
      );
    }
    
    // 模拟分页
    const page = params.page || 1;
    const pageSize = params.pageSize || 10;
    const start = (page - 1) * pageSize;
    const end = start + pageSize;
    const paginatedData = filteredData.slice(start, end);
    
    return {
      code: 200,
      message: '获取成功',
      data: {
        items: paginatedData,
        total: filteredData.length
      }
    };
  },
  
  // 创建
  create: async (data) => {
    console.log('创建合作协议:', data);
    await new Promise(resolve => setTimeout(resolve, 300));
    
    const newAgreement = {
      ...data,
      id: Date.now(),
      created_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
      updated_at: new Date().toISOString().slice(0, 19).replace('T', ' ')
    };
    
    mockAgreements.unshift(newAgreement);
    
    return {
      code: 200,
      message: '创建成功',
      data: newAgreement
    };
  },
  
  // 更新
  update: async (id, data) => {
    console.log('更新合作协议:', id, data);
    await new Promise(resolve => setTimeout(resolve, 300));
    
    const index = mockAgreements.findIndex(item => item.id === id);
    if (index !== -1) {
      mockAgreements[index] = {
        ...mockAgreements[index],
        ...data,
        updated_at: new Date().toISOString().slice(0, 19).replace('T', ' ')
      };
      
      return {
        code: 200,
        message: '更新成功',
        data: mockAgreements[index]
      };
    }
    
    return {
      code: 404,
      message: '合作协议不存在'
    };
  },
  
  // 删除
  delete: async (id) => {
    console.log('删除合作协议:', id);
    await new Promise(resolve => setTimeout(resolve, 300));
    
    const index = mockAgreements.findIndex(item => item.id === id);
    if (index !== -1) {
      mockAgreements.splice(index, 1);
      return {
        code: 200,
        message: '删除成功'
      };
    }
    
    return {
      code: 404,
      message: '合作协议不存在'
    };
  }
};



// ma-crud 配置项
const crudOptions = reactive({
  api: realApi.getList,
  title: "合作协议管理",
  searchLabelWidth: "100px",
  rowSelection: { showCheckedAll: true },
  operationColumn: true,
  actionColumn: {
    width: 180,
  },
  formOption: {
    width: 1000,
    viewType: 'modal'
  },
  table: {
    rowKey: "id",
    border: true,
    size: "medium",
    pagination: {
      showTotal: true,
      showJumper: true,
      showPageSize: true,
    },
  },
  search: {
    collapsed: false,
    showCollapse: true,
    collapsePosition: "left",
  },
  // 搜索前处理参数
  beforeSearch: (params) => {
    return params;
  },
  // 添加前处理参数
  beforeAdd: (params) => {
    return params;
  },
  // 编辑前处理参数
  beforeEdit: (params) => {
    return params;
  },
  add: {
    show: true,
    api: realApi.create,
    text: "新增协议"
  },
  edit: {
    show: true,
    api: realApi.update,
  },
  delete: {
    show: true,
    api: realApi.delete,
  },
});

// 表格列配置
const columns = reactive([
  {
    title: "序号",
    dataIndex: "id",
    width: 80,
    align: "center",
    search: false,
    addDisplay: false,
    editDisplay: false,
  },
  {
    title: "合作协议名称",
    dataIndex: "agreement_name",
    width: 200,
    search: true,
    formType: "input",
    commonRules: [{ required: true, message: '合作协议名称必填' }],
  },
  {
    title: "创建人",
    dataIndex: "submitter",
    width: 120,
    search: true,
    addDisplay: false,  // 新增时不显示
    editDisplay: false, // 编辑时不显示
  },
  {
    title: "提交日期",
    dataIndex: "submit_date",
    width: 120,
    search: false,
    formType: "date",
    commonRules: [{ required: true, message: '提交日期必填' }],
  },
  {
    title: "备注",
    dataIndex: "remark",
    width: 250,
    search: false,
    formType: "textarea",
    rows: 3,
  },
  {
    title: "更新时间",
    dataIndex: "updated_at",
    width: 160,
    search: false,
    addDisplay: false,
    editDisplay: false,
  },
  {
    title: "更新人",
    dataIndex: "updater",
    width: 120,
    search: false,
    addDisplay: false,  // 新增时不显示
    editDisplay: false, // 编辑时不显示
  },
]);

// 生命周期钩子
onMounted(() => {
  console.log('合作协议管理页面已加载');
});
</script>

<style scoped>
.cooperation-agreement {
  height: 100%;
}
</style>
