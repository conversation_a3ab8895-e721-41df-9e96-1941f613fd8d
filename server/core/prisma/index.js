const { prisma } = require('../database/prisma');

class PrismaManager {
  constructor() {
    // 不再需要存储多个客户端实例
    this.sharedClient = prisma;
  }

  /**
   * 获取共享的 Prisma 客户端
   * @param {string} moduleName 模块名称（保留参数以保持向后兼容）
   * @returns {PrismaClient}
   */
  getClient(moduleName) {
    // 记录模块名称用于调试，但返回共享实例
    if (moduleName && process.env.NODE_ENV === 'development') {
      console.log(`模块 ${moduleName} 获取共享 Prisma 客户端`);
    }
    return this.sharedClient;
  }

  /**
   * 断开数据库连接
   * 注意：由于使用共享实例，这个方法现在只是一个占位符
   * 实际的断开连接应该在应用程序关闭时统一处理
   */
  async disconnect() {
    console.log('PrismaManager: 使用共享实例，不执行断开连接操作');
    // 不执行实际的断开连接，因为其他模块可能还在使用
  }
}

module.exports = new PrismaManager();
