const express = require('express');
const { prisma } = require('../database/prisma');
const path = require('path');

/**
 * 基础模块类
 * 所有业务模块都应继承此类
 */
class BaseModule {
  /**
   * @param {Object} config - 模块配置
   * @param {Object} moduleManager - 模块管理器实例
   */
  constructor(config, moduleManager) {
    this.config = config;
    this.moduleManager = moduleManager;
    this.router = express.Router();
    this.prisma = null;
    this.eventHandlers = new Map();
  }

  /**
   * 初始化模块
   * 子类可以重写此方法以添加自定义初始化逻辑
   */
  async init() {
    // 初始化数据库连接
    await this.initDatabase();
    
    // 初始化路由
    await this.initRoutes();
    
    // 初始化事件监听
    await this.initEventListeners();
    
    // 初始化 Swagger 文档
    await this.initSwagger();
  }

  /**
   * 初始化数据库连接
   */
  async initDatabase() {
    try {
      // 使用共享的 Prisma 客户端实例
      this.prisma = prisma;

      // 测试数据库连接
      await this.prisma.$queryRaw`SELECT 1`;
      console.log(`模块 ${this.config.name} 数据库连接成功!`);
    } catch (error) {
      console.error(`模块 ${this.config.name} 数据库连接失败:`, error);
      // 仍然进行模拟连接
      console.log(`[模拟] 初始化数据库连接，模块: ${this.config.name}，模式: ${this.config.schema || '默认'}`);
    }
  }

  /**
   * 初始化路由
   * 子类应该实现此方法来注册自己的路由
   */
  async initRoutes() {
    // 由子类实现
  }

  /**
   * 初始化 Swagger 文档
   * 动态扫描并注册当前模块的所有 API 文档
   */
  async initSwagger() {
    // 动态扫描模式，递归发现所有目录下的路由文件和模型定义
    const swaggerPaths = [
      // 模型定义文件扫描
      path.join(__dirname, '..', '..', 'apps', this.config.name, '**', 'swagger', 'schemas', '*.js'),
      // 路由文件扫描，支持多层嵌套目录
      path.join(__dirname, '..', '..', 'apps', this.config.name, '**', 'routes', '*.js')
    ];
    
    // 注册 Swagger 文档
    this.emit('swagger:register', {
      module: this.config.name,
      paths: swaggerPaths
    });
  }

  /**
   * 初始化事件监听
   * 子类应该实现此方法来注册事件处理器
   */
  async initEventListeners() {
    // 由子类实现
  }

  /**
   * 注册事件处理器
   */
  on(eventName, handler) {
    if (!this.eventHandlers.has(eventName)) {
      this.eventHandlers.set(eventName, []);
    }
    this.eventHandlers.get(eventName).push(handler);
    if (this.moduleManager && typeof this.moduleManager.on === 'function') {
      this.moduleManager.on(eventName, handler);
    }
  }

  /**
   * 触发事件
   */
  emit(eventName, data) {
    if (this.moduleManager && typeof this.moduleManager.emit === 'function') {
      console.log(`[${this.config.name}] 触发事件 ${eventName}:`, data);
      this.moduleManager.emit(eventName, data);
    } else {
      console.log(`[${this.config.name}] 没有 moduleManager，无法触发事件 ${eventName}`);
    }
  }

  /**
   * 获取其他模块实例
   */
  getModule(moduleName) {
    return this.moduleManager.getModule(moduleName);
  }

  /**
   * 清理资源
   */
  async cleanup() {
    // 注意：不要断开共享的 prisma 连接，因为其他模块可能还在使用
    // 只清理模块特定的资源
    this.prisma = null;

    // 移除事件监听
    for (const [eventName, handlers] of this.eventHandlers) {
      for (const handler of handlers) {
        this.moduleManager.removeListener(eventName, handler);
      }
    }
  }
}

module.exports = BaseModule;
