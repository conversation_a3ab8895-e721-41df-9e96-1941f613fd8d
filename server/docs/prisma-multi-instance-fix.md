# Prisma 多实例创建问题修复报告

## 问题描述

在 `/server` 目录下发现多个文件直接创建 `new PrismaClient()` 实例，这违反了单例模式原则，可能导致：
- 数据库连接数过多
- 内存占用增加
- 连接池资源浪费
- 潜在的连接泄漏问题

## 修复原则

所有业务代码应该使用共享的 Prisma 实例：
```javascript
// 正确的导入方式
const { prisma } = require('path/to/core/database/prisma');

// 错误的导入方式 - 已修复
// const { PrismaClient } = require('@prisma/client');
// const prisma = new PrismaClient();
```

## 修复的文件列表

### 1. 核心模块文件
- `server/core/module/BaseModule.js` - 修改为使用共享 prisma 实例
- `server/core/prisma/index.js` - 重构 PrismaManager 类使用共享实例
- `server/config/prisma.ts` - 改为导入共享实例

### 2. 模型文件
- `server/apps/spider/models/SpiderModel.js`
- `server/apps/spider/models/SpiderTaskModel.js`
- `server/apps/master/models/OrderPackageModel.js`

### 3. 控制器文件
- `server/apps/mall/controllers/WechatController.js`
- `server/apps/mall/controllers/UserAuthController.js`

### 4. 服务文件
- `server/apps/provider/services/UserService.js`
- `server/apps/master/system/integration/services/aliyun/AliyunSMSService.js`
- `server/apps/master/system/integration/services/aliyun/AliyunOCRService.js`

### 5. 路由文件
- `server/apps/mall/index.js`
- `server/apps/master/routes/index.js`

### 6. 工厂类和装饰器
- `server/apps/master/system/integration/common/IntegrationFactory.js`
- `server/apps/master/system/log/decorators/LogOperation.js`

### 7. 脚本文件
- `server/scripts/migrations/add-client-to-operation-log.js`

## 修复详情

### BaseModule.js 修复
```javascript
// 修复前
this.prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
});

// 修复后
this.prisma = prisma;
```

### PrismaManager 重构
```javascript
// 修复前
getClient(moduleName) {
  if (!this.clients.has(moduleName)) {
    const client = new PrismaClient({...});
    this.clients.set(moduleName, client);
  }
  return this.clients.get(moduleName);
}

// 修复后
getClient(moduleName) {
  return this.sharedClient; // 返回共享实例
}
```

## 保留的合理实例

以下实例创建是合理的，未进行修改：
1. `server/core/database/prisma.js` - 单例模式的唯一实例创建点
2. `server/core/services/DatabaseNotificationService.js` - 使用原生 pg.Client 监听数据库通知
3. `server/core/utils/RedisUtil.js` - Redis 连接
4. `server/core/services/RabbitMQService.js` - RabbitMQ 连接

## 验证方法

1. 检查所有修改的文件是否正确导入共享实例
2. 确保应用启动时只创建一个 PrismaClient 实例
3. 监控数据库连接数是否符合预期
4. 运行现有测试确保功能正常

## 注意事项

1. 共享实例不应在模块清理时断开连接
2. 应用关闭时统一处理 prisma 连接断开
3. 开发环境下可以通过日志观察实例使用情况

## 后续建议

1. 在代码审查中检查新的 PrismaClient 实例化
2. 考虑添加 ESLint 规则禁止直接创建 PrismaClient
3. 定期检查数据库连接数监控
