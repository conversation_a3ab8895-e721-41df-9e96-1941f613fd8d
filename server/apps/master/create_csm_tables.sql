-- 创建CSM相关表的SQL脚本

-- 主营产品表
CREATE TABLE IF NOT EXISTS csm.csm_main_products (
    id BIGSERIAL PRIMARY KEY,
    product_name VARCHAR(200) NOT NULL,
    submitter VARCHAR(100) NOT NULL,
    submit_date DATE NOT NULL,
    remark TEXT,
    created_at BIGINT NOT NULL,
    updated_at BIGINT NOT NULL,
    updater VARCHAR(100) NOT NULL,
    deleted_at BIGINT
);

-- 企业性质表
CREATE TABLE IF NOT EXISTS csm.csm_enterprise_nature (
    id BIGSERIAL PRIMARY KEY,
    nature_name VARCHAR(200) NOT NULL,
    submitter VARCHAR(100) NOT NULL,
    submit_date DATE NOT NULL,
    remark TEXT,
    created_at BIGINT NOT NULL,
    updated_at BIGINT NOT NULL,
    updater VARCHAR(100) NOT NULL,
    deleted_at BIGINT
);

-- 付款条件表
CREATE TABLE IF NOT EXISTS csm.csm_payment_terms (
    id BIGSERIAL PRIMARY KEY,
    term_name VARCHAR(200) NOT NULL,
    submitter VARCHAR(100) NOT NULL,
    submit_date DATE NOT NULL,
    remark TEXT,
    created_at BIGINT NOT NULL,
    updated_at BIGINT NOT NULL,
    updater VARCHAR(100) NOT NULL,
    deleted_at BIGINT
);

-- 合作协议表
CREATE TABLE IF NOT EXISTS csm.csm_cooperation_agreements (
    id BIGSERIAL PRIMARY KEY,
    agreement_name VARCHAR(200) NOT NULL,
    submitter VARCHAR(100) NOT NULL,
    submit_date DATE NOT NULL,
    remark TEXT,
    created_at BIGINT NOT NULL,
    updated_at BIGINT NOT NULL,
    updater VARCHAR(100) NOT NULL,
    deleted_at BIGINT
);

-- 添加索引
CREATE INDEX IF NOT EXISTS idx_csm_main_products_deleted_at ON csm.csm_main_products(deleted_at);
CREATE INDEX IF NOT EXISTS idx_csm_main_products_product_name ON csm.csm_main_products(product_name);
CREATE INDEX IF NOT EXISTS idx_csm_main_products_submitter ON csm.csm_main_products(submitter);

CREATE INDEX IF NOT EXISTS idx_csm_enterprise_nature_deleted_at ON csm.csm_enterprise_nature(deleted_at);
CREATE INDEX IF NOT EXISTS idx_csm_enterprise_nature_nature_name ON csm.csm_enterprise_nature(nature_name);
CREATE INDEX IF NOT EXISTS idx_csm_enterprise_nature_submitter ON csm.csm_enterprise_nature(submitter);

CREATE INDEX IF NOT EXISTS idx_csm_payment_terms_deleted_at ON csm.csm_payment_terms(deleted_at);
CREATE INDEX IF NOT EXISTS idx_csm_payment_terms_term_name ON csm.csm_payment_terms(term_name);
CREATE INDEX IF NOT EXISTS idx_csm_payment_terms_submitter ON csm.csm_payment_terms(submitter);

CREATE INDEX IF NOT EXISTS idx_csm_cooperation_agreements_deleted_at ON csm.csm_cooperation_agreements(deleted_at);
CREATE INDEX IF NOT EXISTS idx_csm_cooperation_agreements_agreement_name ON csm.csm_cooperation_agreements(agreement_name);
CREATE INDEX IF NOT EXISTS idx_csm_cooperation_agreements_submitter ON csm.csm_cooperation_agreements(submitter);
