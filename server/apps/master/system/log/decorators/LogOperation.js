/**
 * 操作日志装饰器
 */
const { prisma } = require('../../../../../core/database/prisma');
const { generateSnowflakeId } = require('../../../../../shared/utils/snowflake');

/**
 * 操作日志装饰器
 * @param {Object} options 日志选项
 * @returns {Function} 装饰器函数
 */
function LogOperation(options = {}) {
  const { 
    module = 'system', 
    operation = '未知操作',
    logParams = true,
    logResult = false
  } = options;
  
  return function(target, propertyKey, descriptor) {
    const originalMethod = descriptor.value;
    
    descriptor.value = async function(...args) {
      const startTime = Date.now();
      const req = args[0];
      const res = args[1];
      
      try {
        // 执行原始方法
        const result = await originalMethod.apply(this, args);
        
        // 记录操作日志
        await prisma.systemOperationLog.create({
          data: {
            id: generateSnowflakeId(), // 使用雪花算法生成ID
            user_id: BigInt(req.user?.id || 0),
            username: req.user?.username || '未登录用户',
            module,
            operation,
            method: req.method,
            path: req.path,
            ip: req.ip || req.headers['x-forwarded-for'] || req.connection.remoteAddress,
            user_agent: req.headers['user-agent'],
            request_data: logParams ? {
              query: req.query,
              body: req.body,
              params: req.params
            } : undefined,
            response_data: logResult ? result : undefined,
            status: 1,
            execution_time: Date.now() - startTime,
            created_at: BigInt(Date.now())
          }
        });
        
        return result;
      } catch (error) {
        // 记录错误日志
        await prisma.systemOperationLog.create({
          data: {
            id: generateSnowflakeId(), // 使用雪花算法生成ID
            user_id: BigInt(req.user?.id || 0),
            username: req.user?.username || '未登录用户',
            module,
            operation,
            method: req.method,
            path: req.path,
            ip: req.ip || req.headers['x-forwarded-for'] || req.connection.remoteAddress,
            user_agent: req.headers['user-agent'],
            request_data: logParams ? {
              query: req.query,
              body: req.body,
              params: req.params
            } : undefined,
            status: 0,
            error_message: error.message,
            execution_time: Date.now() - startTime,
            created_at: BigInt(Date.now())
          }
        });
        
        throw error;
      }
    };
    
    return descriptor;
  };
}

module.exports = LogOperation;
