/**
 * 阿里云短信服务
 * 处理阿里云短信发送等功能
 */
const Core = require('@alicloud/pop-core');
const { prisma } = require('../../../../../../core/database/prisma');

// 常量定义
const CONFIG_TYPE = 'Aliyun';

class AliyunSMSService {
  constructor(prismaInstance) {
    this.prisma = prismaInstance || prisma;
    this.client = null;
  }

  /**
   * 获取阿里云配置
   * @returns {Promise<Array>} 阿里云配置列表
   */
  async getConfig() {
    try {
      console.log('获取阿里云配置');

      // 从配置表中查询阿里云平台的所有配置
      const configs = await this.prisma.baseSystemConfig.findMany({
        where: {
          config_type: CONFIG_TYPE,
          deleted_at: null
        },
        select: {
          config_key: true,
          config_value: true
        }
      });

      return configs;
    } catch (error) {
      console.error('获取阿里云配置失败:', error);
      throw new Error(`获取阿里云配置失败: ${error.message}`);
    }
  }

  /**
   * 获取SMS客户端实例
   * @returns {Promise<Core>} SMS客户端实例
   */
  async getSMSClient() {
    if (this.client) {
      return this.client;
    }

    try {
      console.log('初始化阿里云SMS客户端');

      // 获取阿里云配置
      const configs = await this.getConfig();
      const accessKeyId = configs.find(item => item.config_key === 'accessKeyId')?.config_value;
      const accessKeySecret = configs.find(item => item.config_key === 'accessKeySecret')?.config_value;
      const endpoint = configs.find(item => item.config_key === 'smsEndpoint')?.config_value || 'https://dysmsapi.aliyuncs.com';
      const apiVersion = configs.find(item => item.config_key === 'smsApiVersion')?.config_value || '2017-05-25';

      if (!accessKeyId || !accessKeySecret) {
        throw new Error('阿里云配置中缺少必要参数');
      }

      // 创建SMS客户端实例
      this.client = new Core({
        accessKeyId,
        accessKeySecret,
        endpoint,
        apiVersion
      });

      return this.client;
    } catch (error) {
      console.error('初始化阿里云SMS客户端失败:', error);
      throw new Error(`初始化阿里云SMS客户端失败: ${error.message}`);
    }
  }

  /**
   * 发送短信
   * @param {string} phoneNumber 手机号码
   * @param {string} templateCode 模板编码
   * @param {Object} templateParam 模板参数
   * @returns {Promise<Object>} 发送结果
   */
  async sendSMS(phoneNumber, templateCode, templateParam) {
    try {
      console.log('发送短信，手机号码:', phoneNumber, '模板编码:', templateCode);

      // 获取阿里云配置
      const configs = await this.getConfig();
      const signName = configs.find(item => item.config_key === 'sms_signName')?.config_value;

      if (!signName) {
        throw new Error('阿里云配置中缺少短信签名');
      }

      // 获取SMS客户端
      const client = await this.getSMSClient();

      // 处理模板参数
      let formattedParam = {};

      // 如果是字符串，尝试解析为JSON
      if (typeof templateParam === 'string') {
        try {
          formattedParam = JSON.parse(templateParam);
        } catch (e) {
          // 如果不是有效的JSON，则将其作为code参数
          formattedParam = { code: templateParam.replace(/[^a-zA-Z0-9]/g, '') };
        }
      }
      // 如果是对象，直接使用
      else if (templateParam && typeof templateParam === 'object') {
        formattedParam = templateParam;

        // 如果有code字段，确保它只包含字母和数字
        if (formattedParam.code) {
          formattedParam.code = String(formattedParam.code).replace(/[^a-zA-Z0-9]/g, '');
        }
      }
      // 如果是空值，创建一个空对象
      else {
        formattedParam = {};
      }

      // 构建请求参数
      const params = {
        PhoneNumbers: phoneNumber,
        SignName: signName,
        TemplateCode: templateCode,
        TemplateParam: JSON.stringify(formattedParam)
      };
      console.log('构建的请求参数:', params);

      // 发送短信
      const result = await client.request('SendSms', params, {
        method: 'POST'
      });

      console.log('短信发送结果:', result);

      // 记录短信发送记录 - 暂时注释掉，避免数据库错误
      // await this.saveSMSRecord(phoneNumber, templateCode, templateParam, result);

      return {
        success: result.Code === 'OK',
        message: result.Message,
        requestId: result.RequestId,
        bizId: result.BizId
      };
    } catch (error) {
      console.error('发送短信失败:', error);

      // 记录短信发送失败记录 - 暂时注释掉，避免数据库错误
      // await this.saveSMSRecord(phoneNumber, templateCode, templateParam, { Code: 'Error', Message: error.message });

      throw new Error(`发送短信失败: ${error.message}`);
    }
  }

  /**
   * 保存短信发送记录
   * 注意：此方法暂时不使用，因为smsRecord表可能不存在
   * @param {string} phoneNumber 手机号码
   * @param {string} templateCode 模板编码
   * @param {Object} templateParam 模板参数
   * @param {Object} result 发送结果
   * @returns {Promise<void>}
   */
  async saveSMSRecord(phoneNumber, templateCode, templateParam, result) {
    try {
      // 检查smsRecord表是否存在
      const tableExists = await this.checkTableExists('smsRecord');
      if (!tableExists) {
        console.warn('smsRecord表不存在，跳过记录保存');
        return;
      }

      const now = BigInt(Date.now());

      await this.prisma.smsRecord.create({
        data: {
          phone_number: phoneNumber,
          template_code: templateCode,
          template_param: typeof templateParam === 'string' ? templateParam : JSON.stringify(templateParam),
          send_status: result.Code === 'OK' ? 1 : 0, // 1成功，0失败
          send_message: result.Message,
          request_id: result.RequestId || '',
          biz_id: result.BizId || '',
          created_at: now,
          updated_at: now
        }
      });
    } catch (error) {
      console.error('保存短信发送记录失败:', error);
      // 这里不抛出异常，避免影响主流程
    }
  }

  /**
   * 检查表是否存在
   * @param {string} tableName 表名
   * @returns {Promise<boolean>} 表是否存在
   */
  async checkTableExists(tableName) {
    try {
      // 这里简单返回false，实际项目中可以根据需要实现
      return false;
    } catch (error) {
      console.error(`检查表${tableName}是否存在失败:`, error);
      return false;
    }
  }
}

module.exports = AliyunSMSService;
