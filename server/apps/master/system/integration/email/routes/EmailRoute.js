/**
 * 邮件路由
 * 处理邮件发送相关请求
 */
const express = require('express');
const router = express.Router();
const { prisma } = require('../../../../../../core/database/prisma');
const EmailController = require('../controllers/EmailController');
const authMiddleware = require('../../../../../../core/middleware/AuthMiddleware');

const controller = new EmailController(prisma);

/**
 * @swagger
 * /api/v1/master/system/integration/email/send:
 *   post:
 *     summary: 发送邮件
 *     description: 发送邮件到指定邮箱
 *     tags:
 *       - 系统管理/集成服务/邮件服务
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               to:
 *                 type: string
 *                 description: 收件人邮箱，多个收件人用逗号分隔
 *                 example: "<EMAIL>"
 *               subject:
 *                 type: string
 *                 description: 邮件主题
 *                 example: "测试邮件"
 *               content:
 *                 type: string
 *                 description: 邮件内容
 *                 example: "这是一封测试邮件。"
 *               isHtml:
 *                 type: boolean
 *                 description: 是否为HTML内容
 *                 example: false
 *               platform:
 *                 type: string
 *                 description: 邮件平台类型（可选，不指定则使用默认平台）
 *                 example: "smtp"
 *             required:
 *               - to
 *               - subject
 *               - content
 *     responses:
 *       200:
 *         description: 成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 0
 *                 message:
 *                   type: string
 *                   example: 邮件发送成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     success:
 *                       type: boolean
 *                       example: true
 *                     message:
 *                       type: string
 *                       example: 发送成功
 *                     messageId:
 *                       type: string
 *                       example: "<<EMAIL>>"
 *       400:
 *         description: 参数错误
 *       500:
 *         description: 服务器错误
 */
router.post('/send', authMiddleware, (req, res) => {
  controller.sendEmail(req, res);
});

/**
 * @swagger
 * /api/v1/master/system/integration/email/platform-test-connection:
 *   post:
 *     summary: 测试邮件平台连接
 *     description: 测试指定邮件平台的连接是否正常
 *     tags:
 *       - 系统管理/集成服务/邮件服务
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               platform:
 *                 type: string
 *                 description: 邮件平台类型
 *                 example: "smtp"
 *               sendTestEmail:
 *                 type: boolean
 *                 description: 是否发送测试邮件
 *                 example: false
 *               testEmail:
 *                 type: string
 *                 description: 测试邮件接收地址
 *                 example: "<EMAIL>"
 *             required:
 *               - platform
 *     responses:
 *       200:
 *         description: 成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 0
 *                 message:
 *                   type: string
 *                   example: 连接测试成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     success:
 *                       type: boolean
 *                       example: true
 *                     message:
 *                       type: string
 *                       example: 连接成功
 *       400:
 *         description: 参数错误
 *       500:
 *         description: 服务器错误
 */
router.post('/platform-test-connection', authMiddleware, (req, res) => {
  controller.testConnection(req, res);
});

module.exports = router;
