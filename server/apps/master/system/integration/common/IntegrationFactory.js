/**
 * 集成服务工厂
 * 用于创建和管理各种集成服务实例
 */
const { prisma } = require('../../../../../core/database/prisma');
const ConfigurationService = require('../../configure/services/ConfigurationService');

class IntegrationFactory {
  /**
   * 构造函数
   * @param {Object} prismaInstance Prisma客户端实例（可选，默认使用共享实例）
   */
  constructor(prismaInstance) {
    this.prisma = prismaInstance || prisma;
    this.configService = new ConfigurationService(this.prisma);
    this.serviceInstances = {};
  }

  /**
   * 获取服务实例
   * @param {string} integrationType 集成类型
   * @param {string} serviceType 服务类型
   * @returns {Promise<Object>} 服务实例
   */
  async getService(integrationType, serviceType) {
    try {
      // 缓存键
      const cacheKey = `${integrationType}:${serviceType}`;
      
      // 如果已有缓存实例，直接返回
      if (this.serviceInstances[cacheKey]) {
        return this.serviceInstances[cacheKey];
      }
      
      // 获取配置
      const configs = await this.configService.getConfigsByType(integrationType);
      const config = configs.find(item => item.config_key === serviceType);
      
      if (!config) {
        throw new Error(`未找到服务配置: ${integrationType}.${serviceType}`);
      }
      
      // 创建服务实例
      const ServiceClass = this.getServiceClass(integrationType, serviceType);
      const serviceInstance = new ServiceClass(config, this.prisma);
      
      // 初始化服务
      await serviceInstance.initialize();
      
      // 缓存服务实例
      this.serviceInstances[cacheKey] = serviceInstance;
      
      return serviceInstance;
    } catch (error) {
      console.error(`获取服务实例失败: ${integrationType}.${serviceType}`, error);
      throw new Error(`获取服务实例失败: ${error.message}`);
    }
  }

  /**
   * 获取默认服务实例
   * @param {string} integrationType 集成类型
   * @returns {Promise<Object>} 默认服务实例
   */
  async getDefaultService(integrationType) {
    try {
      // 获取默认配置
      const defaultConfig = await this.configService.getDefaultConfig(integrationType);
      
      if (!defaultConfig) {
        throw new Error(`未找到默认${integrationType}服务配置`);
      }
      
      // 获取服务实例
      return await this.getService(integrationType, defaultConfig.config_key);
    } catch (error) {
      console.error(`获取默认服务实例失败: ${integrationType}`, error);
      throw new Error(`获取默认服务实例失败: ${error.message}`);
    }
  }

  /**
   * 获取服务类
   * @param {string} integrationType 集成类型
   * @param {string} serviceType 服务类型
   * @returns {Class} 服务类
   */
  getServiceClass(integrationType, serviceType) {
    try {
      // 根据集成类型和服务类型动态加载服务类
      // 这里假设服务类按照一定的命名规则和目录结构组织
      
      switch (integrationType) {
        case 'Upload':
          // 上传服务
          switch (serviceType) {
            case 'local':
              return require('../../integration/upload/services/LocalStorageService');
            case 'aliyun':
              return require('../../integration/upload/services/AliyunOSSUploadService');
            case 'tencent':
              return require('../../integration/upload/services/future/TencentCOSUploadService');
            case 'qiniu':
              return require('../../integration/upload/services/future/QiniuUploadService');
            default:
              throw new Error(`不支持的上传服务类型: ${serviceType}`);
          }
          
        case 'SMS':
          // 短信服务
          switch (serviceType) {
            case 'aliyun':
              return require('../../integration/sms/services/AliyunSMSService');
            case 'tencent':
              return require('../../integration/sms/services/TencentSMSService');
            case 'netease':
              return require('../../integration/sms/services/NeteaseSMSService');
            default:
              throw new Error(`不支持的短信服务类型: ${serviceType}`);
          }
          
        case 'OAuth':
          // OAuth服务
          switch (serviceType) {
            case 'wechat':
              return require('../../integration/oauth/services/WechatOAuthService');
            case 'alipay':
              return require('../../integration/oauth/services/AlipayOAuthService');
            default:
              throw new Error(`不支持的OAuth服务类型: ${serviceType}`);
          }
          
        default:
          throw new Error(`不支持的集成类型: ${integrationType}`);
      }
    } catch (error) {
      console.error(`获取服务类失败: ${integrationType}.${serviceType}`, error);
      throw new Error(`获取服务类失败: ${error.message}`);
    }
  }

  /**
   * 测试服务连接
   * @param {string} integrationType 集成类型
   * @param {string} serviceType 服务类型
   * @param {Object} testData 测试数据
   * @returns {Promise<Object>} 测试结果
   */
  async testService(integrationType, serviceType, testData = {}) {
    try {
      // 获取配置
      const configs = await this.configService.getConfigsByType(integrationType);
      const config = configs.find(item => item.config_key === serviceType);
      
      if (!config) {
        throw new Error(`未找到服务配置: ${integrationType}.${serviceType}`);
      }
      
      // 创建临时服务实例用于测试
      const ServiceClass = this.getServiceClass(integrationType, serviceType);
      const serviceInstance = new ServiceClass(config, this.prisma);
      
      // 测试连接
      return await serviceInstance.testConnection(testData);
    } catch (error) {
      console.error(`测试服务连接失败: ${integrationType}.${serviceType}`, error);
      throw new Error(`测试服务连接失败: ${error.message}`);
    }
  }

  /**
   * 清除服务实例缓存
   * @param {string} integrationType 集成类型
   * @param {string} serviceType 服务类型
   */
  clearServiceCache(integrationType, serviceType = null) {
    if (serviceType) {
      // 清除特定服务实例缓存
      const cacheKey = `${integrationType}:${serviceType}`;
      delete this.serviceInstances[cacheKey];
      console.log(`已清除服务实例缓存: ${cacheKey}`);
    } else {
      // 清除指定集成类型的所有服务实例缓存
      Object.keys(this.serviceInstances).forEach(key => {
        if (key.startsWith(`${integrationType}:`)) {
          delete this.serviceInstances[key];
        }
      });
      console.log(`已清除所有${integrationType}服务实例缓存`);
    }
  }
}

module.exports = IntegrationFactory;
