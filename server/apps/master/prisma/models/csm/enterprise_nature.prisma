/// 企业性质表，存储供应商基本信息管理中的企业性质数据
model CsmEnterpriseNature {
  // 主键
  id            BigInt    @id @default(autoincrement())        /// 企业性质ID，自增主键
  
  // 基本信息
  nature_name   String    @db.VarChar(200)                    /// 企业性质名称，必填，最大200字符
  submitter     String    @db.VarChar(100)                    /// 提交人，必填，最大100字符
  submit_date   DateTime  @db.Date                            /// 提交日期，必填
  remark        String?   @db.Text                            /// 备注信息，选填
  updater       String    @db.VarChar(100)                    /// 更新人，必填，最大100字符
  
  // 审计字段
  created_at    BigInt                                        /// 创建时间戳（毫秒）
  updated_at    BigInt                                        /// 更新时间戳（毫秒）
  deleted_at    BigInt?                                       /// 删除时间戳（毫秒）（软删除）

  // 索引
  @@index([deleted_at])
  @@index([nature_name])
  @@index([submitter])
  
  @@map("csm_enterprise_nature")
  @@schema("csm")
}
