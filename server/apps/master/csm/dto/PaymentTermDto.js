const Joi = require('joi');

/**
 * 付款条件DTO
 */
class PaymentTermDto {
  /**
   * 创建付款条件验证规则
   */
  static createSchema = Joi.object({
    term_name: Joi.string().max(200).required().messages({
      'string.empty': '付款条件名称不能为空',
      'string.max': '付款条件名称不能超过200个字符',
      'any.required': '付款条件名称必填'
    }),
    submitter: Joi.string().max(100).required().messages({
      'string.empty': '提交人不能为空',
      'string.max': '提交人不能超过100个字符',
      'any.required': '提交人必填'
    }),
    submit_date: Joi.date().required().messages({
      'date.base': '提交日期格式不正确',
      'any.required': '提交日期必填'
    }),
    remark: Joi.string().allow('', null).optional(),
    updater: Joi.string().max(100).required().messages({
      'string.empty': '更新人不能为空',
      'string.max': '更新人不能超过100个字符',
      'any.required': '更新人必填'
    })
  });

  /**
   * 更新付款条件验证规则
   */
  static updateSchema = Joi.object({
    term_name: Joi.string().max(200).optional().messages({
      'string.empty': '付款条件名称不能为空',
      'string.max': '付款条件名称不能超过200个字符'
    }),
    submitter: Joi.string().max(100).optional().messages({
      'string.empty': '提交人不能为空',
      'string.max': '提交人不能超过100个字符'
    }),
    submit_date: Joi.date().optional().messages({
      'date.base': '提交日期格式不正确'
    }),
    remark: Joi.string().allow('', null).optional(),
    updater: Joi.string().max(100).required().messages({
      'string.empty': '更新人不能为空',
      'string.max': '更新人不能超过100个字符',
      'any.required': '更新人必填'
    })
  });

  /**
   * 查询参数验证规则
   */
  static querySchema = Joi.object({
    page: Joi.number().integer().min(1).default(1),
    pageSize: Joi.number().integer().min(1).max(100).default(10),
    term_name: Joi.string().allow('').optional(),
    submitter: Joi.string().allow('').optional()
  });
}

module.exports = PaymentTermDto;
