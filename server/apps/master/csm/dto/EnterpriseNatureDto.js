const Joi = require('joi');

/**
 * 企业性质DTO
 */
class EnterpriseNatureDto {
  /**
   * 创建企业性质验证规则
   */
  static createSchema = Joi.object({
    nature_name: Joi.string().max(200).required().messages({
      'string.empty': '企业性质名称不能为空',
      'string.max': '企业性质名称不能超过200个字符',
      'any.required': '企业性质名称必填'
    }),
    submitter: Joi.string().max(100).required().messages({
      'string.empty': '提交人不能为空',
      'string.max': '提交人不能超过100个字符',
      'any.required': '提交人必填'
    }),
    submit_date: Joi.date().required().messages({
      'date.base': '提交日期格式不正确',
      'any.required': '提交日期必填'
    }),
    remark: Joi.string().allow('', null).optional(),
    updater: Joi.string().max(100).required().messages({
      'string.empty': '更新人不能为空',
      'string.max': '更新人不能超过100个字符',
      'any.required': '更新人必填'
    })
  });

  /**
   * 更新企业性质验证规则
   */
  static updateSchema = Joi.object({
    nature_name: Joi.string().max(200).optional().messages({
      'string.empty': '企业性质名称不能为空',
      'string.max': '企业性质名称不能超过200个字符'
    }),
    submitter: Joi.string().max(100).optional().messages({
      'string.empty': '提交人不能为空',
      'string.max': '提交人不能超过100个字符'
    }),
    submit_date: Joi.date().optional().messages({
      'date.base': '提交日期格式不正确'
    }),
    remark: Joi.string().allow('', null).optional(),
    updater: Joi.string().max(100).required().messages({
      'string.empty': '更新人不能为空',
      'string.max': '更新人不能超过100个字符',
      'any.required': '更新人必填'
    })
  });

  /**
   * 查询参数验证规则
   */
  static querySchema = Joi.object({
    page: Joi.number().integer().min(1).default(1),
    pageSize: Joi.number().integer().min(1).max(100).default(10),
    nature_name: Joi.string().allow('').optional(),
    submitter: Joi.string().allow('').optional()
  });
}

module.exports = EnterpriseNatureDto;
