const Joi = require('joi');

/**
 * 企业性质DTO
 */
class EnterpriseNatureDto {
  /**
   * 创建企业性质验证规则
   */
  static createSchema = Joi.object({
    nature_name: Joi.string().max(200).required().messages({
      'string.empty': '企业性质名称不能为空',
      'string.max': '企业性质名称不能超过200个字符',
      'any.required': '企业性质名称必填'
    }),
    submit_date: Joi.date().required().messages({
      'date.base': '提交日期格式不正确',
      'any.required': '提交日期必填'
    }),
    remark: Joi.string().allow('', null).optional(),
    // 忽略用户提交的字段，后端自动处理
    submitter: Joi.any().strip(),
    updater: Joi.any().strip()
  });

  /**
   * 更新企业性质验证规则
   */
  static updateSchema = Joi.object({
    id: Joi.any().strip(), // 忽略id字段
    nature_name: Joi.string().max(200).optional().messages({
      'string.empty': '企业性质名称不能为空',
      'string.max': '企业性质名称不能超过200个字符'
    }),
    submit_date: Joi.date().optional().messages({
      'date.base': '提交日期格式不正确'
    }),
    remark: Joi.string().allow('', null).optional(),
    // 忽略用户提交的字段，后端自动处理
    submitter: Joi.any().strip(),
    updater: Joi.any().strip(),
    created_at: Joi.any().strip(), // 忽略创建时间
    updated_at: Joi.any().strip()  // 忽略更新时间
  });

  /**
   * 查询参数验证规则
   */
  static querySchema = Joi.object({
    page: Joi.number().integer().min(1).default(1),
    pageSize: Joi.number().integer().min(1).max(100).default(10),
    nature_name: Joi.string().allow('').optional(),
    submitter: Joi.string().allow('').optional()
  });
}

module.exports = EnterpriseNatureDto;
