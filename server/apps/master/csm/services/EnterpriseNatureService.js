const EnterpriseNatureModel = require('../models/EnterpriseNatureModel');
const EnterpriseNatureDto = require('../dto/EnterpriseNatureDto');

/**
 * 企业性质服务
 */
class EnterpriseNatureService {
  constructor() {
    this.enterpriseNatureModel = new EnterpriseNatureModel();
  }

  /**
   * 获取企业性质列表
   * @param {Object} params 查询参数
   * @returns {Promise<Object>} 分页结果
   */
  async getList(params) {
    // 验证查询参数
    const { error, value } = EnterpriseNatureDto.querySchema.validate(params);
    if (error) {
      throw new Error(`参数验证失败: ${error.details[0].message}`);
    }

    const result = await this.enterpriseNatureModel.getList(value);
    
    // 格式化返回数据
    return {
      code: 200,
      message: '获取成功',
      data: {
        items: result.items.map(item => ({
          ...item,
          // 格式化日期显示
          submit_date: item.submit_date ? item.submit_date.toISOString().split('T')[0] : null,
          updated_at: item.updated_at ? new Date(item.updated_at).toISOString().slice(0, 19).replace('T', ' ') : null
        })),
        total: result.total,
        page: result.page,
        pageSize: result.pageSize
      }
    };
  }

  /**
   * 根据ID获取企业性质
   * @param {number} id 企业性质ID
   * @returns {Promise<Object>} 企业性质信息
   */
  async getById(id) {
    if (!id || isNaN(id)) {
      throw new Error('无效的ID');
    }

    const item = await this.enterpriseNatureModel.getById(id);
    if (!item) {
      return {
        code: 404,
        message: '企业性质不存在'
      };
    }

    return {
      code: 200,
      message: '获取成功',
      data: {
        ...item,
        submit_date: item.submit_date ? item.submit_date.toISOString().split('T')[0] : null,
        updated_at: item.updated_at ? new Date(item.updated_at).toISOString().slice(0, 19).replace('T', ' ') : null
      }
    };
  }

  /**
   * 创建企业性质
   * @param {Object} data 企业性质数据
   * @param {Object} user 当前用户信息
   * @returns {Promise<Object>} 创建结果
   */
  async create(data, user) {
    // 验证数据
    const { error, value } = EnterpriseNatureDto.createSchema.validate(data);
    if (error) {
      throw new Error(`数据验证失败: ${error.details[0].message}`);
    }

    // 处理日期格式
    if (value.submit_date) {
      value.submit_date = new Date(value.submit_date);
    }

    // 自动设置提交人和更新人信息
    const currentUser = user?.nickname || user?.username || '系统用户';
    value.submitter = currentUser;
    value.updater = currentUser;

    const item = await this.enterpriseNatureModel.create(value);

    return {
      code: 200,
      message: '创建成功',
      data: {
        ...item,
        submit_date: item.submit_date ? item.submit_date.toISOString().split('T')[0] : null,
        updated_at: item.updated_at ? new Date(item.updated_at).toISOString().slice(0, 19).replace('T', ' ') : null
      }
    };
  }

  /**
   * 更新企业性质
   * @param {number} id 企业性质ID
   * @param {Object} data 更新数据
   * @param {Object} user 当前用户信息
   * @returns {Promise<Object>} 更新结果
   */
  async update(id, data, user) {
    if (!id || isNaN(id)) {
      throw new Error('无效的ID');
    }

    // 验证数据
    const { error, value } = EnterpriseNatureDto.updateSchema.validate(data);
    if (error) {
      throw new Error(`数据验证失败: ${error.details[0].message}`);
    }

    // 处理日期格式
    if (value.submit_date) {
      value.submit_date = new Date(value.submit_date);
    }

    // 自动设置更新人信息
    const currentUser = user?.nickname || user?.username || '系统用户';
    value.updater = currentUser;

    const item = await this.enterpriseNatureModel.update(id, value);
    if (!item) {
      return {
        code: 404,
        message: '企业性质不存在'
      };
    }

    return {
      code: 200,
      message: '更新成功',
      data: {
        ...item,
        submit_date: item.submit_date ? item.submit_date.toISOString().split('T')[0] : null,
        updated_at: item.updated_at ? new Date(item.updated_at).toISOString().slice(0, 19).replace('T', ' ') : null
      }
    };
  }

  /**
   * 删除企业性质
   * @param {number} id 企业性质ID
   * @returns {Promise<Object>} 删除结果
   */
  async delete(id) {
    if (!id || isNaN(id)) {
      throw new Error('无效的ID');
    }

    const success = await this.enterpriseNatureModel.delete(id);
    if (!success) {
      return {
        code: 404,
        message: '企业性质不存在'
      };
    }

    return {
      code: 200,
      message: '删除成功'
    };
  }

  /**
   * 批量删除企业性质
   * @param {Array<number>} ids 企业性质ID数组
   * @returns {Promise<Object>} 删除结果
   */
  async batchDelete(ids) {
    if (!Array.isArray(ids) || ids.length === 0) {
      throw new Error('无效的ID数组');
    }

    // 验证所有ID都是数字
    const validIds = ids.filter(id => !isNaN(id) && id > 0);
    if (validIds.length === 0) {
      throw new Error('没有有效的ID');
    }

    const count = await this.enterpriseNatureModel.batchDelete(validIds);

    return {
      code: 200,
      message: `成功删除 ${count} 条记录`,
      data: { count }
    };
  }
}

module.exports = EnterpriseNatureService;
