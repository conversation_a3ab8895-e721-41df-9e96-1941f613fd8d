const MainProductModel = require('../models/MainProductModel');
const MainProductDto = require('../dto/MainProductDto');

/**
 * 主营产品服务
 */
class MainProductService {
  constructor() {
    this.mainProductModel = new MainProductModel();
  }

  /**
   * 获取主营产品列表
   * @param {Object} params 查询参数
   * @returns {Promise<Object>} 分页结果
   */
  async getList(params) {
    // 验证查询参数
    const { error, value } = MainProductDto.querySchema.validate(params);
    if (error) {
      throw new Error(`参数验证失败: ${error.details[0].message}`);
    }

    const result = await this.mainProductModel.getList(value);
    
    // 格式化返回数据
    return {
      code: 200,
      message: '获取成功',
      data: {
        items: result.items.map(item => ({
          ...item,
          // 格式化日期显示
          submit_date: item.submit_date ? item.submit_date.toISOString().split('T')[0] : null,
          updated_at: item.updated_at ? new Date(item.updated_at).toISOString().slice(0, 19).replace('T', ' ') : null
        })),
        total: result.total,
        page: result.page,
        pageSize: result.pageSize
      }
    };
  }

  /**
   * 根据ID获取主营产品
   * @param {number} id 主营产品ID
   * @returns {Promise<Object>} 主营产品信息
   */
  async getById(id) {
    if (!id || isNaN(id)) {
      throw new Error('无效的ID');
    }

    const item = await this.mainProductModel.getById(id);
    if (!item) {
      return {
        code: 404,
        message: '主营产品不存在'
      };
    }

    return {
      code: 200,
      message: '获取成功',
      data: {
        ...item,
        submit_date: item.submit_date ? item.submit_date.toISOString().split('T')[0] : null,
        updated_at: item.updated_at ? new Date(item.updated_at).toISOString().slice(0, 19).replace('T', ' ') : null
      }
    };
  }

  /**
   * 创建主营产品
   * @param {Object} data 主营产品数据
   * @returns {Promise<Object>} 创建结果
   */
  async create(data) {
    // 验证数据
    const { error, value } = MainProductDto.createSchema.validate(data);
    if (error) {
      throw new Error(`数据验证失败: ${error.details[0].message}`);
    }

    // 处理日期格式
    if (value.submit_date) {
      value.submit_date = new Date(value.submit_date);
    }

    const item = await this.mainProductModel.create(value);

    return {
      code: 200,
      message: '创建成功',
      data: {
        ...item,
        submit_date: item.submit_date ? item.submit_date.toISOString().split('T')[0] : null,
        updated_at: item.updated_at ? new Date(item.updated_at).toISOString().slice(0, 19).replace('T', ' ') : null
      }
    };
  }

  /**
   * 更新主营产品
   * @param {number} id 主营产品ID
   * @param {Object} data 更新数据
   * @returns {Promise<Object>} 更新结果
   */
  async update(id, data) {
    if (!id || isNaN(id)) {
      throw new Error('无效的ID');
    }

    // 验证数据
    const { error, value } = MainProductDto.updateSchema.validate(data);
    if (error) {
      throw new Error(`数据验证失败: ${error.details[0].message}`);
    }

    // 处理日期格式
    if (value.submit_date) {
      value.submit_date = new Date(value.submit_date);
    }

    const item = await this.mainProductModel.update(id, value);
    if (!item) {
      return {
        code: 404,
        message: '主营产品不存在'
      };
    }

    return {
      code: 200,
      message: '更新成功',
      data: {
        ...item,
        submit_date: item.submit_date ? item.submit_date.toISOString().split('T')[0] : null,
        updated_at: item.updated_at ? new Date(item.updated_at).toISOString().slice(0, 19).replace('T', ' ') : null
      }
    };
  }

  /**
   * 删除主营产品
   * @param {number} id 主营产品ID
   * @returns {Promise<Object>} 删除结果
   */
  async delete(id) {
    if (!id || isNaN(id)) {
      throw new Error('无效的ID');
    }

    const success = await this.mainProductModel.delete(id);
    if (!success) {
      return {
        code: 404,
        message: '主营产品不存在'
      };
    }

    return {
      code: 200,
      message: '删除成功'
    };
  }

  /**
   * 批量删除主营产品
   * @param {Array<number>} ids 主营产品ID数组
   * @returns {Promise<Object>} 删除结果
   */
  async batchDelete(ids) {
    if (!Array.isArray(ids) || ids.length === 0) {
      throw new Error('无效的ID数组');
    }

    // 验证所有ID都是数字
    const validIds = ids.filter(id => !isNaN(id) && id > 0);
    if (validIds.length === 0) {
      throw new Error('没有有效的ID');
    }

    const count = await this.mainProductModel.batchDelete(validIds);

    return {
      code: 200,
      message: `成功删除 ${count} 条记录`,
      data: { count }
    };
  }
}

module.exports = MainProductService;
