const CooperationAgreementModel = require('../models/CooperationAgreementModel');
const CooperationAgreementDto = require('../dto/CooperationAgreementDto');

/**
 * 合作协议服务
 */
class CooperationAgreementService {
  constructor() {
    this.cooperationAgreementModel = new CooperationAgreementModel();
  }

  /**
   * 获取合作协议列表
   * @param {Object} params 查询参数
   * @returns {Promise<Object>} 分页结果
   */
  async getList(params) {
    // 验证查询参数
    const { error, value } = CooperationAgreementDto.querySchema.validate(params);
    if (error) {
      throw new Error(`参数验证失败: ${error.details[0].message}`);
    }

    const result = await this.cooperationAgreementModel.getList(value);
    
    // 格式化返回数据
    return {
      code: 200,
      message: '获取成功',
      data: {
        items: result.items.map(item => ({
          ...item,
          // 格式化日期显示
          submit_date: item.submit_date ? item.submit_date.toISOString().split('T')[0] : null,
          updated_at: item.updated_at ? new Date(item.updated_at).toISOString().slice(0, 19).replace('T', ' ') : null
        })),
        total: result.total,
        page: result.page,
        pageSize: result.pageSize
      }
    };
  }

  /**
   * 根据ID获取合作协议
   * @param {number} id 合作协议ID
   * @returns {Promise<Object>} 合作协议信息
   */
  async getById(id) {
    if (!id || isNaN(id)) {
      throw new Error('无效的ID');
    }

    const item = await this.cooperationAgreementModel.getById(id);
    if (!item) {
      return {
        code: 404,
        message: '合作协议不存在'
      };
    }

    return {
      code: 200,
      message: '获取成功',
      data: {
        ...item,
        submit_date: item.submit_date ? item.submit_date.toISOString().split('T')[0] : null,
        updated_at: item.updated_at ? new Date(item.updated_at).toISOString().slice(0, 19).replace('T', ' ') : null
      }
    };
  }

  /**
   * 创建合作协议
   * @param {Object} data 合作协议数据
   * @returns {Promise<Object>} 创建结果
   */
  async create(data) {
    // 验证数据
    const { error, value } = CooperationAgreementDto.createSchema.validate(data);
    if (error) {
      throw new Error(`数据验证失败: ${error.details[0].message}`);
    }

    // 处理日期格式
    if (value.submit_date) {
      value.submit_date = new Date(value.submit_date);
    }

    const item = await this.cooperationAgreementModel.create(value);

    return {
      code: 200,
      message: '创建成功',
      data: {
        ...item,
        submit_date: item.submit_date ? item.submit_date.toISOString().split('T')[0] : null,
        updated_at: item.updated_at ? new Date(item.updated_at).toISOString().slice(0, 19).replace('T', ' ') : null
      }
    };
  }

  /**
   * 更新合作协议
   * @param {number} id 合作协议ID
   * @param {Object} data 更新数据
   * @returns {Promise<Object>} 更新结果
   */
  async update(id, data) {
    if (!id || isNaN(id)) {
      throw new Error('无效的ID');
    }

    // 验证数据
    const { error, value } = CooperationAgreementDto.updateSchema.validate(data);
    if (error) {
      throw new Error(`数据验证失败: ${error.details[0].message}`);
    }

    // 处理日期格式
    if (value.submit_date) {
      value.submit_date = new Date(value.submit_date);
    }

    const item = await this.cooperationAgreementModel.update(id, value);
    if (!item) {
      return {
        code: 404,
        message: '合作协议不存在'
      };
    }

    return {
      code: 200,
      message: '更新成功',
      data: {
        ...item,
        submit_date: item.submit_date ? item.submit_date.toISOString().split('T')[0] : null,
        updated_at: item.updated_at ? new Date(item.updated_at).toISOString().slice(0, 19).replace('T', ' ') : null
      }
    };
  }

  /**
   * 删除合作协议
   * @param {number} id 合作协议ID
   * @returns {Promise<Object>} 删除结果
   */
  async delete(id) {
    if (!id || isNaN(id)) {
      throw new Error('无效的ID');
    }

    const success = await this.cooperationAgreementModel.delete(id);
    if (!success) {
      return {
        code: 404,
        message: '合作协议不存在'
      };
    }

    return {
      code: 200,
      message: '删除成功'
    };
  }

  /**
   * 批量删除合作协议
   * @param {Array<number>} ids 合作协议ID数组
   * @returns {Promise<Object>} 删除结果
   */
  async batchDelete(ids) {
    if (!Array.isArray(ids) || ids.length === 0) {
      throw new Error('无效的ID数组');
    }

    // 验证所有ID都是数字
    const validIds = ids.filter(id => !isNaN(id) && id > 0);
    if (validIds.length === 0) {
      throw new Error('没有有效的ID');
    }

    const count = await this.cooperationAgreementModel.batchDelete(validIds);

    return {
      code: 200,
      message: `成功删除 ${count} 条记录`,
      data: { count }
    };
  }
}

module.exports = CooperationAgreementService;
