const express = require('express');
const EnterpriseNatureController = require('../controllers/EnterpriseNatureController');

const router = express.Router();
const enterpriseNatureController = new EnterpriseNatureController();

/**
 * 企业性质路由
 */

// 获取企业性质列表
router.get('/', (req, res) => enterpriseNatureController.getList(req, res));

// 根据ID获取企业性质
router.get('/:id', (req, res) => enterpriseNatureController.getById(req, res));

// 创建企业性质
router.post('/', (req, res) => enterpriseNatureController.create(req, res));

// 更新企业性质
router.put('/:id', (req, res) => enterpriseNatureController.update(req, res));

// 删除企业性质
router.delete('/:id', (req, res) => enterpriseNatureController.delete(req, res));

// 批量删除企业性质
router.post('/batch-delete', (req, res) => enterpriseNatureController.batchDelete(req, res));

module.exports = router;
