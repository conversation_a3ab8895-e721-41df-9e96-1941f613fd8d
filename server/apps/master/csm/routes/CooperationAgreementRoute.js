const express = require('express');
const CooperationAgreementController = require('../controllers/CooperationAgreementController');

const router = express.Router();
const cooperationAgreementController = new CooperationAgreementController();

/**
 * 合作协议路由
 */

// 获取合作协议列表
router.get('/', (req, res) => cooperationAgreementController.getList(req, res));

// 根据ID获取合作协议
router.get('/:id', (req, res) => cooperationAgreementController.getById(req, res));

// 创建合作协议
router.post('/', (req, res) => cooperationAgreementController.create(req, res));

// 更新合作协议
router.put('/:id', (req, res) => cooperationAgreementController.update(req, res));

// 删除合作协议
router.delete('/:id', (req, res) => cooperationAgreementController.delete(req, res));

// 批量删除合作协议
router.post('/batch-delete', (req, res) => cooperationAgreementController.batchDelete(req, res));

module.exports = router;
