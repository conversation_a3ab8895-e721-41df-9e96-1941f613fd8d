const express = require('express');
const MainProductController = require('../controllers/MainProductController');

const router = express.Router();
const mainProductController = new MainProductController();

/**
 * 主营产品路由
 */

// 获取主营产品列表
router.get('/', (req, res) => mainProductController.getList(req, res));

// 根据ID获取主营产品
router.get('/:id', (req, res) => mainProductController.getById(req, res));

// 创建主营产品
router.post('/', (req, res) => mainProductController.create(req, res));

// 更新主营产品
router.put('/:id', (req, res) => mainProductController.update(req, res));

// 删除主营产品
router.delete('/:id', (req, res) => mainProductController.delete(req, res));

// 批量删除主营产品
router.post('/batch-delete', (req, res) => mainProductController.batchDelete(req, res));

module.exports = router;
