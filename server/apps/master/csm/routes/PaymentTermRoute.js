const express = require('express');
const PaymentTermController = require('../controllers/PaymentTermController');

const router = express.Router();
const paymentTermController = new PaymentTermController();

/**
 * 付款条件路由
 */

// 获取付款条件列表
router.get('/', (req, res) => paymentTermController.getList(req, res));

// 根据ID获取付款条件
router.get('/:id', (req, res) => paymentTermController.getById(req, res));

// 创建付款条件
router.post('/', (req, res) => paymentTermController.create(req, res));

// 更新付款条件
router.put('/:id', (req, res) => paymentTermController.update(req, res));

// 删除付款条件
router.delete('/:id', (req, res) => paymentTermController.delete(req, res));

// 批量删除付款条件
router.post('/batch-delete', (req, res) => paymentTermController.batchDelete(req, res));

module.exports = router;
