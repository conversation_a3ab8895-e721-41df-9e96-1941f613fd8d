const express = require('express');
const MainProductRoute = require('./MainProductRoute');
const EnterpriseNatureRoute = require('./EnterpriseNatureRoute');
const PaymentTermRoute = require('./PaymentTermRoute');
const CooperationAgreementRoute = require('./CooperationAgreementRoute');

const router = express.Router();

/**
 * CSM (Customer Supplier Management) 供应商基本信息管理路由
 */

// 主营产品路由
router.use('/main-products', MainProductRoute);

// 企业性质路由
router.use('/enterprise-nature', EnterpriseNatureRoute);

// 付款条件路由
router.use('/payment-terms', PaymentTermRoute);

// 合作协议路由
router.use('/cooperation-agreements', CooperationAgreementRoute);

module.exports = router;
