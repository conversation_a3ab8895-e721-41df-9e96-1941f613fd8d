const { prisma } = require('../../../../core/database/prisma');

/**
 * 付款条件模型
 */
class PaymentTermModel {
  constructor() {
    this.prisma = prisma;
  }

  /**
   * 获取付款条件列表
   * @param {Object} params 查询参数
   * @returns {Promise<Object>} 分页结果
   */
  async getList(params = {}) {
    const { page = 1, pageSize = 10, term_name, submitter } = params;
    const skip = (page - 1) * pageSize;

    // 构建查询条件
    const where = {
      deleted_at: null
    };

    if (term_name) {
      where.term_name = {
        contains: term_name
      };
    }

    if (submitter) {
      where.submitter = {
        contains: submitter
      };
    }

    // 获取总数
    const total = await this.prisma.csmPaymentTerm.count({ where });

    // 获取列表数据
    const items = await this.prisma.csmPaymentTerm.findMany({
      where,
      skip,
      take: pageSize,
      orderBy: {
        created_at: 'desc'
      }
    });

    return {
      items: items.map(item => ({
        ...item,
        id: Number(item.id),
        created_at: Number(item.created_at),
        updated_at: Number(item.updated_at),
        deleted_at: item.deleted_at ? Number(item.deleted_at) : null
      })),
      total,
      page,
      pageSize
    };
  }

  /**
   * 根据ID获取付款条件
   * @param {number} id 付款条件ID
   * @returns {Promise<Object|null>} 付款条件信息
   */
  async getById(id) {
    const item = await this.prisma.csmPaymentTerm.findFirst({
      where: {
        id: BigInt(id),
        deleted_at: null
      }
    });

    if (!item) return null;

    return {
      ...item,
      id: Number(item.id),
      created_at: Number(item.created_at),
      updated_at: Number(item.updated_at),
      deleted_at: item.deleted_at ? Number(item.deleted_at) : null
    };
  }

  /**
   * 创建付款条件
   * @param {Object} data 付款条件数据
   * @returns {Promise<Object>} 创建的付款条件
   */
  async create(data) {
    const now = Date.now();
    const item = await this.prisma.csmPaymentTerm.create({
      data: {
        ...data,
        created_at: BigInt(now),
        updated_at: BigInt(now)
      }
    });

    return {
      ...item,
      id: Number(item.id),
      created_at: Number(item.created_at),
      updated_at: Number(item.updated_at),
      deleted_at: item.deleted_at ? Number(item.deleted_at) : null
    };
  }

  /**
   * 更新付款条件
   * @param {number} id 付款条件ID
   * @param {Object} data 更新数据
   * @returns {Promise<Object|null>} 更新后的付款条件
   */
  async update(id, data) {
    const now = Date.now();
    
    try {
      // 先检查记录是否存在且未被删除
      const existingItem = await this.prisma.csmPaymentTerm.findFirst({
        where: {
          id: BigInt(id),
          deleted_at: null
        }
      });

      if (!existingItem) {
        return null;
      }

      // 执行更新操作
      const item = await this.prisma.csmPaymentTerm.update({
        where: {
          id: BigInt(id)
        },
        data: {
          ...data,
          updated_at: BigInt(now)
        }
      });

      return {
        ...item,
        id: Number(item.id),
        created_at: Number(item.created_at),
        updated_at: Number(item.updated_at),
        deleted_at: item.deleted_at ? Number(item.deleted_at) : null
      };
    } catch (error) {
      if (error.code === 'P2025') {
        return null; // 记录不存在
      }
      throw error;
    }
  }

  /**
   * 删除付款条件（软删除）
   * @param {number} id 付款条件ID
   * @returns {Promise<boolean>} 删除是否成功
   */
  async delete(id) {
    const now = Date.now();
    
    try {
      await this.prisma.csmPaymentTerm.update({
        where: {
          id: BigInt(id),
          deleted_at: null
        },
        data: {
          deleted_at: BigInt(now)
        }
      });
      return true;
    } catch (error) {
      if (error.code === 'P2025') {
        return false; // 记录不存在
      }
      throw error;
    }
  }

  /**
   * 批量删除付款条件（软删除）
   * @param {Array<number>} ids 付款条件ID数组
   * @returns {Promise<number>} 删除的记录数
   */
  async batchDelete(ids) {
    const now = Date.now();
    
    const result = await this.prisma.csmPaymentTerm.updateMany({
      where: {
        id: {
          in: ids.map(id => BigInt(id))
        },
        deleted_at: null
      },
      data: {
        deleted_at: BigInt(now)
      }
    });

    return result.count;
  }
}

module.exports = PaymentTermModel;
