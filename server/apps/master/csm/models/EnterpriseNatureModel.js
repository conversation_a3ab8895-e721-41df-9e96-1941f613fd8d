const { prisma } = require('../../../../core/database/prisma');

/**
 * 企业性质模型
 */
class EnterpriseNatureModel {
  constructor() {
    this.prisma = prisma;
  }

  /**
   * 获取企业性质列表
   * @param {Object} params 查询参数
   * @returns {Promise<Object>} 分页结果
   */
  async getList(params = {}) {
    const { page = 1, pageSize = 10, nature_name, submitter } = params;
    const skip = (page - 1) * pageSize;

    // 构建查询条件
    const where = {
      deleted_at: null
    };

    if (nature_name) {
      where.nature_name = {
        contains: nature_name
      };
    }

    if (submitter) {
      where.submitter = {
        contains: submitter
      };
    }

    // 获取总数
    const total = await this.prisma.csmEnterpriseNature.count({ where });

    // 获取列表数据
    const items = await this.prisma.csmEnterpriseNature.findMany({
      where,
      skip,
      take: pageSize,
      orderBy: {
        created_at: 'desc'
      }
    });

    return {
      items: items.map(item => ({
        ...item,
        id: Number(item.id),
        created_at: Number(item.created_at),
        updated_at: Number(item.updated_at),
        deleted_at: item.deleted_at ? Number(item.deleted_at) : null
      })),
      total,
      page,
      pageSize
    };
  }

  /**
   * 根据ID获取企业性质
   * @param {number} id 企业性质ID
   * @returns {Promise<Object|null>} 企业性质信息
   */
  async getById(id) {
    const item = await this.prisma.csmEnterpriseNature.findFirst({
      where: {
        id: BigInt(id),
        deleted_at: null
      }
    });

    if (!item) return null;

    return {
      ...item,
      id: Number(item.id),
      created_at: Number(item.created_at),
      updated_at: Number(item.updated_at),
      deleted_at: item.deleted_at ? Number(item.deleted_at) : null
    };
  }

  /**
   * 创建企业性质
   * @param {Object} data 企业性质数据
   * @returns {Promise<Object>} 创建的企业性质
   */
  async create(data) {
    const now = Date.now();
    const item = await this.prisma.csmEnterpriseNature.create({
      data: {
        ...data,
        created_at: BigInt(now),
        updated_at: BigInt(now)
      }
    });

    return {
      ...item,
      id: Number(item.id),
      created_at: Number(item.created_at),
      updated_at: Number(item.updated_at),
      deleted_at: item.deleted_at ? Number(item.deleted_at) : null
    };
  }

  /**
   * 更新企业性质
   * @param {number} id 企业性质ID
   * @param {Object} data 更新数据
   * @returns {Promise<Object|null>} 更新后的企业性质
   */
  async update(id, data) {
    const now = Date.now();
    
    try {
      const item = await this.prisma.csmEnterpriseNature.update({
        where: {
          id: BigInt(id),
          deleted_at: null
        },
        data: {
          ...data,
          updated_at: BigInt(now)
        }
      });

      return {
        ...item,
        id: Number(item.id),
        created_at: Number(item.created_at),
        updated_at: Number(item.updated_at),
        deleted_at: item.deleted_at ? Number(item.deleted_at) : null
      };
    } catch (error) {
      if (error.code === 'P2025') {
        return null; // 记录不存在
      }
      throw error;
    }
  }

  /**
   * 删除企业性质（软删除）
   * @param {number} id 企业性质ID
   * @returns {Promise<boolean>} 删除是否成功
   */
  async delete(id) {
    const now = Date.now();
    
    try {
      await this.prisma.csmEnterpriseNature.update({
        where: {
          id: BigInt(id),
          deleted_at: null
        },
        data: {
          deleted_at: BigInt(now)
        }
      });
      return true;
    } catch (error) {
      if (error.code === 'P2025') {
        return false; // 记录不存在
      }
      throw error;
    }
  }

  /**
   * 批量删除企业性质（软删除）
   * @param {Array<number>} ids 企业性质ID数组
   * @returns {Promise<number>} 删除的记录数
   */
  async batchDelete(ids) {
    const now = Date.now();
    
    const result = await this.prisma.csmEnterpriseNature.updateMany({
      where: {
        id: {
          in: ids.map(id => BigInt(id))
        },
        deleted_at: null
      },
      data: {
        deleted_at: BigInt(now)
      }
    });

    return result.count;
  }
}

module.exports = EnterpriseNatureModel;
