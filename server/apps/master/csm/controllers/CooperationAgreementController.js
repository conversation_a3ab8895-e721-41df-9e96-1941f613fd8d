const CooperationAgreementService = require('../services/CooperationAgreementService');

/**
 * 合作协议控制器
 */
class CooperationAgreementController {
  constructor() {
    this.cooperationAgreementService = new CooperationAgreementService();
  }

  /**
   * 获取合作协议列表
   */
  async getList(req, res) {
    try {
      const result = await this.cooperationAgreementService.getList(req.query);
      res.json(result);
    } catch (error) {
      console.error('获取合作协议列表失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '获取合作协议列表失败'
      });
    }
  }

  /**
   * 根据ID获取合作协议
   */
  async getById(req, res) {
    try {
      const { id } = req.params;
      const result = await this.cooperationAgreementService.getById(parseInt(id));
      
      if (result.code === 404) {
        return res.status(404).json(result);
      }
      
      res.json(result);
    } catch (error) {
      console.error('获取合作协议详情失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '获取合作协议详情失败'
      });
    }
  }

  /**
   * 创建合作协议
   */
  async create(req, res) {
    try {
      const result = await this.cooperationAgreementService.create(req.body, req.user);
      res.status(201).json(result);
    } catch (error) {
      console.error('创建合作协议失败:', error);
      res.status(400).json({
        code: 400,
        message: error.message || '创建合作协议失败'
      });
    }
  }

  /**
   * 更新合作协议
   */
  async update(req, res) {
    try {
      const { id } = req.params;
      const result = await this.cooperationAgreementService.update(parseInt(id), req.body, req.user);

      if (result.code === 404) {
        return res.status(404).json(result);
      }

      res.json(result);
    } catch (error) {
      console.error('更新合作协议失败:', error);
      res.status(400).json({
        code: 400,
        message: error.message || '更新合作协议失败'
      });
    }
  }

  /**
   * 删除合作协议
   */
  async delete(req, res) {
    try {
      const { id } = req.params;
      const result = await this.cooperationAgreementService.delete(parseInt(id));
      
      if (result.code === 404) {
        return res.status(404).json(result);
      }
      
      res.json(result);
    } catch (error) {
      console.error('删除合作协议失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '删除合作协议失败'
      });
    }
  }

  /**
   * 批量删除合作协议
   */
  async batchDelete(req, res) {
    try {
      const { ids } = req.body;
      const result = await this.cooperationAgreementService.batchDelete(ids);
      res.json(result);
    } catch (error) {
      console.error('批量删除合作协议失败:', error);
      res.status(400).json({
        code: 400,
        message: error.message || '批量删除合作协议失败'
      });
    }
  }
}

module.exports = CooperationAgreementController;
