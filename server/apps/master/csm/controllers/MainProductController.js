const MainProductService = require('../services/MainProductService');

/**
 * 主营产品控制器
 */
class MainProductController {
  constructor() {
    this.mainProductService = new MainProductService();
  }

  /**
   * 获取主营产品列表
   */
  async getList(req, res) {
    try {
      const result = await this.mainProductService.getList(req.query);
      res.json(result);
    } catch (error) {
      console.error('获取主营产品列表失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '获取主营产品列表失败'
      });
    }
  }

  /**
   * 根据ID获取主营产品
   */
  async getById(req, res) {
    try {
      const { id } = req.params;
      const result = await this.mainProductService.getById(parseInt(id));
      
      if (result.code === 404) {
        return res.status(404).json(result);
      }
      
      res.json(result);
    } catch (error) {
      console.error('获取主营产品详情失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '获取主营产品详情失败'
      });
    }
  }

  /**
   * 创建主营产品
   */
  async create(req, res) {
    try {
      const result = await this.mainProductService.create(req.body, req.user);
      res.status(201).json(result);
    } catch (error) {
      console.error('创建主营产品失败:', error);
      res.status(400).json({
        code: 400,
        message: error.message || '创建主营产品失败'
      });
    }
  }

  /**
   * 更新主营产品
   */
  async update(req, res) {
    try {
      const { id } = req.params;
      const result = await this.mainProductService.update(parseInt(id), req.body, req.user);

      if (result.code === 404) {
        return res.status(404).json(result);
      }

      res.json(result);
    } catch (error) {
      console.error('更新主营产品失败:', error);
      res.status(400).json({
        code: 400,
        message: error.message || '更新主营产品失败'
      });
    }
  }

  /**
   * 删除主营产品
   */
  async delete(req, res) {
    try {
      const { id } = req.params;
      const result = await this.mainProductService.delete(parseInt(id));
      
      if (result.code === 404) {
        return res.status(404).json(result);
      }
      
      res.json(result);
    } catch (error) {
      console.error('删除主营产品失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '删除主营产品失败'
      });
    }
  }

  /**
   * 批量删除主营产品
   */
  async batchDelete(req, res) {
    try {
      const { ids } = req.body;
      const result = await this.mainProductService.batchDelete(ids);
      res.json(result);
    } catch (error) {
      console.error('批量删除主营产品失败:', error);
      res.status(400).json({
        code: 400,
        message: error.message || '批量删除主营产品失败'
      });
    }
  }
}

module.exports = MainProductController;
