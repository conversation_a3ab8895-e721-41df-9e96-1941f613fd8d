const EnterpriseNatureService = require('../services/EnterpriseNatureService');

/**
 * 企业性质控制器
 */
class EnterpriseNatureController {
  constructor() {
    this.enterpriseNatureService = new EnterpriseNatureService();
  }

  /**
   * 获取企业性质列表
   */
  async getList(req, res) {
    try {
      const result = await this.enterpriseNatureService.getList(req.query);
      res.json(result);
    } catch (error) {
      console.error('获取企业性质列表失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '获取企业性质列表失败'
      });
    }
  }

  /**
   * 根据ID获取企业性质
   */
  async getById(req, res) {
    try {
      const { id } = req.params;
      const result = await this.enterpriseNatureService.getById(parseInt(id));
      
      if (result.code === 404) {
        return res.status(404).json(result);
      }
      
      res.json(result);
    } catch (error) {
      console.error('获取企业性质详情失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '获取企业性质详情失败'
      });
    }
  }

  /**
   * 创建企业性质
   */
  async create(req, res) {
    try {
      const result = await this.enterpriseNatureService.create(req.body, req.user);
      res.status(201).json(result);
    } catch (error) {
      console.error('创建企业性质失败:', error);
      res.status(400).json({
        code: 400,
        message: error.message || '创建企业性质失败'
      });
    }
  }

  /**
   * 更新企业性质
   */
  async update(req, res) {
    try {
      const { id } = req.params;
      const result = await this.enterpriseNatureService.update(parseInt(id), req.body, req.user);

      if (result.code === 404) {
        return res.status(404).json(result);
      }

      res.json(result);
    } catch (error) {
      console.error('更新企业性质失败:', error);
      res.status(400).json({
        code: 400,
        message: error.message || '更新企业性质失败'
      });
    }
  }

  /**
   * 删除企业性质
   */
  async delete(req, res) {
    try {
      const { id } = req.params;
      const result = await this.enterpriseNatureService.delete(parseInt(id));
      
      if (result.code === 404) {
        return res.status(404).json(result);
      }
      
      res.json(result);
    } catch (error) {
      console.error('删除企业性质失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '删除企业性质失败'
      });
    }
  }

  /**
   * 批量删除企业性质
   */
  async batchDelete(req, res) {
    try {
      const { ids } = req.body;
      const result = await this.enterpriseNatureService.batchDelete(ids);
      res.json(result);
    } catch (error) {
      console.error('批量删除企业性质失败:', error);
      res.status(400).json({
        code: 400,
        message: error.message || '批量删除企业性质失败'
      });
    }
  }
}

module.exports = EnterpriseNatureController;
