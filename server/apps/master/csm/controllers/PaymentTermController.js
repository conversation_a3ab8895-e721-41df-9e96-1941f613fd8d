const PaymentTermService = require('../services/PaymentTermService');

/**
 * 付款条件控制器
 */
class PaymentTermController {
  constructor() {
    this.paymentTermService = new PaymentTermService();
  }

  /**
   * 获取付款条件列表
   */
  async getList(req, res) {
    try {
      const result = await this.paymentTermService.getList(req.query);
      res.json(result);
    } catch (error) {
      console.error('获取付款条件列表失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '获取付款条件列表失败'
      });
    }
  }

  /**
   * 根据ID获取付款条件
   */
  async getById(req, res) {
    try {
      const { id } = req.params;
      const result = await this.paymentTermService.getById(parseInt(id));
      
      if (result.code === 404) {
        return res.status(404).json(result);
      }
      
      res.json(result);
    } catch (error) {
      console.error('获取付款条件详情失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '获取付款条件详情失败'
      });
    }
  }

  /**
   * 创建付款条件
   */
  async create(req, res) {
    try {
      const result = await this.paymentTermService.create(req.body, req.user);
      res.status(201).json(result);
    } catch (error) {
      console.error('创建付款条件失败:', error);
      res.status(400).json({
        code: 400,
        message: error.message || '创建付款条件失败'
      });
    }
  }

  /**
   * 更新付款条件
   */
  async update(req, res) {
    try {
      const { id } = req.params;
      const result = await this.paymentTermService.update(parseInt(id), req.body, req.user);

      if (result.code === 404) {
        return res.status(404).json(result);
      }

      res.json(result);
    } catch (error) {
      console.error('更新付款条件失败:', error);
      res.status(400).json({
        code: 400,
        message: error.message || '更新付款条件失败'
      });
    }
  }

  /**
   * 删除付款条件
   */
  async delete(req, res) {
    try {
      const { id } = req.params;
      const result = await this.paymentTermService.delete(parseInt(id));
      
      if (result.code === 404) {
        return res.status(404).json(result);
      }
      
      res.json(result);
    } catch (error) {
      console.error('删除付款条件失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '删除付款条件失败'
      });
    }
  }

  /**
   * 批量删除付款条件
   */
  async batchDelete(req, res) {
    try {
      const { ids } = req.body;
      const result = await this.paymentTermService.batchDelete(ids);
      res.json(result);
    } catch (error) {
      console.error('批量删除付款条件失败:', error);
      res.status(400).json({
        code: 400,
        message: error.message || '批量删除付款条件失败'
      });
    }
  }
}

module.exports = PaymentTermController;
