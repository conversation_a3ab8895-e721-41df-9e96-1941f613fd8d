/**
 * 商城用户认证控制器
 * 处理用户登录、注册、退出登录、修改密码等认证相关功能
 */
const MallBaseController = require('./base/MallBaseController');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const redisUtil = require('../../../core/utils/RedisUtil');
const authConfig = require('../../../config/auth.config');
const JWT_SECRET = authConfig.jwt.secret;
const JWT_EXPIRES_IN = authConfig.jwt.expire;

class UserAuthController extends MallBaseController {
  /**
   * 用户注册
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async register(req, res) {
    try {
      // 验证必填字段
      const requiredFields = ['username', 'password', 'phone', 'captcha'];
      const validation = this.validateFields(req.body, requiredFields);
      if (!validation.valid) {
        return this.fail(res, '用户名、密码、手机号和验证码不能为空', 400);
      }

      const { username, phone, captcha } = req.body;

      // 验证手机号格式
      if (!this.validatePhoneFormat(phone)) {
        return this.fail(res, '手机号格式不正确', 400);
      }

      // 验证验证码
      let codeKey;
      try {
        console.log('开始验证验证码，手机号:', phone, '验证码:', captcha);
        
        // 检查Redis连接状态
        console.log('Redis连接状态:', redisUtil.isConnected ? '已连接' : '未连接');
        
        // 直接从 Redis 中获取验证码进行比较
        codeKey = `verification_code:register:${phone}`;
        console.log('验证码键:', codeKey);
        
        const savedCode = await redisUtil.getClient().get(codeKey);
        console.log('Redis中存储的验证码:', savedCode || '无');
        
        if (savedCode) {
          console.log('验证码比较:');
          console.log(`- 输入验证码: ${captcha}, 类型: ${typeof captcha}, 长度: ${captcha.length}`);
          console.log(`- 存储验证码: ${savedCode}, 类型: ${typeof savedCode}, 长度: ${savedCode.length}`);
          
          if (savedCode !== captcha) {
            console.log('验证码不匹配');
            return this.fail(res, '验证码错误', 400);
          }
          
          console.log('验证码验证成功');
        } else {
          console.log('验证码不存在或已过期');
          return this.fail(res, '验证码不存在或已过期，请重新获取', 400);
        }
      } catch (error) {
        console.error('验证验证码失败:', error);
        return this.fail(res, '验证验证码失败: ' + error.message, 500);
      }

      // 检查用户名是否已存在
      try {
        const existingUsers = await this.prisma.$queryRaw`
          SELECT id FROM "base"."mall_user" 
          WHERE "username" = ${username}
          AND "deleted_at" IS NULL
        `;
        
        if (existingUsers && existingUsers.length > 0) {
          return this.fail(res, '用户名已存在', 400);
        }
      } catch (queryError) {
        console.error('查询用户名是否存在失败:', queryError);
        return this.fail(res, '验证用户名失败，请稍后再试', 500);
      }

      // 检查手机号是否已存在
      try {
        const existingUsers = await this.prisma.$queryRaw`
          SELECT id FROM "base"."mall_user" 
          WHERE "phone" = ${phone}
          AND "deleted_at" IS NULL
        `;
        
        if (existingUsers && existingUsers.length > 0) {
          return this.fail(res, '手机号已被注册', 400);
        }
      } catch (queryError) {
        console.error('查询手机号是否存在失败:', queryError);
        return this.fail(res, '验证手机号失败，请稍后再试', 500);
      }

      // 创建用户
      try {
        // 加密密码
        const { password, nickname, email } = req.body;
        const salt = await bcrypt.genSalt(10);
        const hashedPassword = await bcrypt.hash(password, salt);
        
        // 使用事务处理创建用户
        let createdUser;
        try {
          // 删除验证码（事务外执行，因为Redis操作不参与数据库事务）
          if (codeKey) {
            await redisUtil.getClient().del(codeKey);
          }
          await this.prisma.$transaction(async (prisma) => {
            // 创建用户并获取返回的用户对象
            createdUser = await this.userService.createWithTransaction(prisma, {
              username,
              password: hashedPassword,
              nickname,
              phone,
              email,
              status: 1,
              created_at: Date.now(),
              updated_at: Date.now()
            });
            
            // 如果需要在事务中执行其他相关操作，可以在这里添加
            // 例如：创建用户钱包、初始化用户设置等
          }, {
            // 设置事务隔离级别为串行化，确保数据一致性
            isolationLevel: this.prisma.$transaction.SERIALIZABLE
          });
          // 返回成功响应
          return this.success(res, {
            user: {
              id: createdUser.id.toString(),
              username,
              nickname: nickname || username,
              phone,
              email: email || null,
              created_at: createdUser.created_at
            }
          }, '注册成功');
        } catch (transactionError) {
          // 事务失败，自动回滚所有数据库操作
          console.error('用户注册事务失败，数据已回滚:', transactionError);
          
          // 根据错误类型返回不同的错误信息
          if (transactionError.code === 'P2002') {
            // 唯一性约束冲突
            return this.fail(res, '用户名或手机号已存在', 400);
          } else {
            return this.fail(res, '注册失败，请稍后再试', 500);
          }
        }
      } catch (createError) {
        console.error('创建用户失败:', createError);
        return this.fail(res, '注册失败，请稍后再试', 500);
      }
    } catch (error) {
      console.error('注册过程中出错:', error);
      const { message, code } = this.handleDbError(error);
      return this.fail(res, message, code);
    }
  }

  /**
   * 用户登录
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async login(req, res) {
    try {
      const { username, password } = req.body;
      
      // 验证用户名和密码不为空
      if (!username || !password) {
        return this.fail(res, '用户名和密码不能为空', 400);
      }
      
      // 查询用户信息
      try {
        const users = await this.prisma.$queryRaw`
          SELECT id, username, password, nickname, phone, email, status, avatar, wechat_openid
          FROM "base"."mall_user" 
          WHERE ("username" = ${username} OR "phone" = ${username})
          AND "deleted_at" IS NULL
        `;
        
        if (!users || users.length === 0) {
          return this.fail(res, '用户名或密码错误', 401);
        }
        
        const user = users[0];
        
        // 检查用户状态
        if (user.status !== 1) {
          return this.fail(res, '账号已被禁用，请联系客服', 403);
        }
        
        // 验证密码
        const isPasswordValid = await bcrypt.compare(password, user.password);
        if (!isPasswordValid) {
          return this.fail(res, '用户名或密码错误', 401);
        }
        
        // 生成JWT令牌
        const token = jwt.sign(
          { id: user.id.toString(), username: user.username },
          JWT_SECRET,
          { expiresIn: JWT_EXPIRES_IN }
        );
        
        // 计算令牌过期时间（秒）
        const expiresIn = parseInt(JWT_EXPIRES_IN.replace(/[^0-9]/g, '')) * (
          JWT_EXPIRES_IN.includes('h') ? 3600 : 
          JWT_EXPIRES_IN.includes('d') ? 86400 : 
          JWT_EXPIRES_IN.includes('m') ? 60 : 1
        );
        
        // 保存用户令牌（用于单点登录）
        await redisUtil.saveUserToken(user.id.toString(), token, expiresIn);
        
        // 获取用户最近登录信息
        const now = Date.now();
        let loginCount = 1;
        let lastLoginIp = req.ip || req.headers['x-forwarded-for'] || req.connection.remoteAddress;
        let lastLoginTime = now;
        
        try {
          // 更新用户登录信息
          await this.prisma.$executeRaw`
            UPDATE "base"."mall_user"
            SET "last_login_ip" = ${lastLoginIp}, "last_login_time" = ${now}, "login_count" = COALESCE("login_count", 0) + 1, "updated_at" = ${now}
            WHERE "id" = ${user.id}
          `;
          
          // 重新查询用户信息以获取更新后的登录计数
          const updatedUsers = await this.prisma.$queryRaw`
            SELECT login_count, last_login_ip, last_login_time, avatar FROM "base"."mall_user"
            WHERE "id" = ${user.id}
          `;
          
          if (updatedUsers && updatedUsers.length > 0) {
            loginCount = updatedUsers[0].login_count || 1;
            lastLoginIp = updatedUsers[0].last_login_ip || lastLoginIp;
            lastLoginTime = updatedUsers[0].last_login_time || now;
          }
        } catch (updateError) {
          console.warn('更新用户登录信息失败:', updateError);
          // 继续执行，不影响登录流程
        }
        
        // 返回用户信息和令牌
        return this.success(res, {
          token,
          user: {
            id: user.id.toString(),
            username: user.username,
            nickname: user.nickname || user.username,
            phone: user.phone,
            email: user.email,
            avatar: user.avatar || '',
            last_login_time: lastLoginTime,
            last_login_ip: lastLoginIp,
            login_count: loginCount,
            wechatOpenid: user.wechat_openid || ''
          }
        }, '登录成功');
      } catch (queryError) {
        console.error('查询用户信息失败:', queryError);
        return this.fail(res, '登录失败，请稍后再试', 500);
      }
    } catch (error) {
      console.error('登录过程中出错:', error);
      return this.fail(res, '登录失败，请稍后再试', 500);
    }
  }

  /**
   * 用户退出登录
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async logout(req, res) {
    try {
      // 获取用户ID和令牌
      const userId = this.getUserId(req);
      
      if (!userId) {
        return this.fail(res, '未登录状态', 401);
      }
      
      try {
        // 获取令牌
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
          return this.fail(res, '无效的令牌', 401);
        }
        
        const token = authHeader.split(' ')[1];
        
        // 解析令牌以获取过期时间
        const decoded = jwt.verify(token, JWT_SECRET);
        const currentTime = Math.floor(Date.now() / 1000);
        const expirationTime = decoded.exp;
        const remainingTime = expirationTime - currentTime;
        
        if (remainingTime > 0) {
          // 将令牌加入黑名单，有效期为令牌的剩余有效期
          await redisUtil.addTokenToBlacklist(token, remainingTime);
        }
        
        // 删除用户令牌映射
        await redisUtil.removeUserToken(userId.toString(), token);
        
        return this.success(res, {}, '退出登录成功');
      } catch (error) {
        console.error('退出登录失败:', error);
        return this.fail(res, '退出登录失败: ' + error.message, 500);
      }
    } catch (error) {
      console.error('退出登录过程中出错:', error);
      return this.fail(res, '退出登录失败，请稍后再试', 500);
    }
  }

  /**
   * 修改密码
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async changePassword(req, res) {
    try {
      // 获取用户ID
      const userId = this.getUserId(req);
      
      if (!userId) {
        return this.fail(res, '未登录状态，无法修改密码', 401);
      }
      
      // 验证必填字段
      const { oldPassword, newPassword } = req.body;
      if (!oldPassword || !newPassword) {
        return this.fail(res, '原密码和新密码不能为空', 400);
      }
      
      // 验证新密码长度
      if (newPassword.length < 6 || newPassword.length > 20) {
        return this.fail(res, '新密码长度应为6-20个字符', 400);
      }
      
      // 查询用户当前密码
      try {
        const users = await this.prisma.$queryRaw`
          SELECT id, password FROM "base"."mall_user" 
          WHERE "id" = ${userId}
          AND "deleted_at" IS NULL
        `;
        
        if (!users || users.length === 0) {
          return this.fail(res, '用户不存在或已被删除', 400);
        }
        
        const user = users[0];
        
        // 验证原密码
        const isPasswordValid = await bcrypt.compare(oldPassword, user.password);
        if (!isPasswordValid) {
          return this.fail(res, '原密码错误', 400);
        }
        
        // 加密新密码
        const salt = await bcrypt.genSalt(10);
        const hashedPassword = await bcrypt.hash(newPassword, salt);
        
        // 更新密码
        const now = Date.now();
        await this.prisma.$executeRaw`
          UPDATE "base"."mall_user"
          SET "password" = ${hashedPassword}, "updated_at" = ${now}
          WHERE "id" = ${userId}
        `;
        
        // 获取当前令牌并加入黑名单（强制重新登录）
        const token = req.headers.authorization.split(' ')[1];
        
        // 解析令牌以获取过期时间
        const decoded = jwt.verify(token, JWT_SECRET);
        const currentTime = Math.floor(Date.now() / 1000);
        const expirationTime = decoded.exp;
        const remainingTime = expirationTime - currentTime;
        
        if (remainingTime > 0) {
          // 将令牌加入黑名单，有效期为令牌的剩余有效期
          await redisUtil.addTokenToBlacklist(token, remainingTime);
        }
        
        // 删除用户令牌映射
        await redisUtil.removeUserToken(userId.toString(), token);
        
        return this.success(res, {}, '密码修改成功，请重新登录');
      } catch (error) {
        console.error('修改密码失败:', error);
        return this.fail(res, '修改密码失败，请稍后再试', 500);
      }
    } catch (error) {
      console.error('修改密码过程中出错:', error);
      return this.fail(res, '修改密码失败，请稍后再试', 500);
    }
  }

  /**
   * 短信验证码登录
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async smsLogin(req, res) {
    try {
      const { phone, smsCode } = req.body;

      // 验证手机号和验证码不为空
      if (!phone || !smsCode) {
        return this.fail(res, '手机号和验证码不能为空', 400);
      }

      // 验证手机号格式
      if (!this.validatePhoneFormat(phone)) {
        return this.fail(res, '手机号格式不正确', 400);
      }

      // 验证验证码
      try {
        console.log('开始验证短信验证码，手机号:', phone, '验证码:', smsCode);

        // 检查Redis连接状态
        console.log('Redis连接状态:', redisUtil.isConnected ? '已连接' : '未连接');

        // 直接从 Redis 中获取验证码进行比较
        const codeKey = `verification_code:login:${phone}`;
        console.log('验证码键:', codeKey);

        const savedCode = await redisUtil.getClient().get(codeKey);
        console.log('Redis中存储的验证码:', savedCode || '无');

        if (savedCode) {
          console.log('验证码比较:');
          console.log(`- 输入验证码: ${smsCode}, 类型: ${typeof smsCode}, 长度: ${smsCode.length}`);
          console.log(`- 存储验证码: ${savedCode}, 类型: ${typeof savedCode}, 长度: ${savedCode.length}`);

          if (savedCode !== smsCode) {
            console.log('验证码不匹配');
            return this.fail(res, '验证码错误', 400);
          }

          console.log('验证码验证成功');

          // 验证成功后删除验证码
          await redisUtil.getClient().del(codeKey);
        } else {
          console.log('验证码不存在或已过期');
          return this.fail(res, '验证码不存在或已过期，请重新获取', 400);
        }
      } catch (error) {
        console.error('验证验证码失败:', error);
        return this.fail(res, '验证验证码失败: ' + error.message, 500);
      }

      // 查询用户信息
      try {
        const users = await this.prisma.$queryRaw`
          SELECT id, username, password, nickname, phone, email, status, avatar, wechat_openid
          FROM "base"."mall_user"
          WHERE "phone" = ${phone}
          AND "deleted_at" IS NULL
        `;

        if (!users || users.length === 0) {
          return this.fail(res, '手机号未注册，请先注册', 401);
        }

        const user = users[0];

        // 检查用户状态
        if (user.status !== 1) {
          return this.fail(res, '账号已被禁用，请联系客服', 403);
        }

        // 生成JWT令牌
        const token = jwt.sign(
          { id: user.id.toString(), username: user.username },
          JWT_SECRET,
          { expiresIn: JWT_EXPIRES_IN }
        );

        // 计算令牌过期时间（秒）
        const expiresIn = parseInt(JWT_EXPIRES_IN.replace(/[^0-9]/g, '')) * (
          JWT_EXPIRES_IN.includes('h') ? 3600 :
          JWT_EXPIRES_IN.includes('d') ? 86400 :
          JWT_EXPIRES_IN.includes('m') ? 60 : 1
        );

        // 保存用户令牌（用于单点登录）
        await redisUtil.saveUserToken(user.id.toString(), token, expiresIn);

        // 获取用户最近登录信息
        const now = Date.now();
        let loginCount = 1;
        let lastLoginIp = req.ip || req.headers['x-forwarded-for'] || req.connection.remoteAddress;
        let lastLoginTime = now;

        try {
          // 更新用户登录信息
          await this.prisma.$executeRaw`
            UPDATE "base"."mall_user"
            SET "last_login_ip" = ${lastLoginIp}, "last_login_time" = ${now}, "login_count" = COALESCE("login_count", 0) + 1, "updated_at" = ${now}
            WHERE "id" = ${user.id}
          `;

          // 重新查询用户信息以获取更新后的登录计数
          const updatedUsers = await this.prisma.$queryRaw`
            SELECT login_count, last_login_ip, last_login_time, avatar FROM "base"."mall_user"
            WHERE "id" = ${user.id}
          `;

          if (updatedUsers && updatedUsers.length > 0) {
            loginCount = updatedUsers[0].login_count || 1;
            lastLoginIp = updatedUsers[0].last_login_ip || lastLoginIp;
            lastLoginTime = updatedUsers[0].last_login_time || now;
          }
        } catch (updateError) {
          console.warn('更新用户登录信息失败:', updateError);
          // 继续执行，不影响登录流程
        }

        // 返回用户信息和令牌
        return this.success(res, {
          token,
          user: {
            id: user.id.toString(),
            username: user.username,
            nickname: user.nickname || user.username,
            phone: user.phone,
            email: user.email,
            avatar: user.avatar || '',
            last_login_time: lastLoginTime,
            last_login_ip: lastLoginIp,
            login_count: loginCount,
            wechatOpenid: user.wechat_openid || ''
          }
        }, '短信登录成功');
      } catch (queryError) {
        console.error('查询用户信息失败:', queryError);
        return this.fail(res, '登录失败，请稍后再试', 500);
      }
    } catch (error) {
      console.error('短信登录过程中出错:', error);
      return this.fail(res, '短信登录失败，请稍后再试', 500);
    }
  }

  /**
   * 忘记密码重置
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async resetPassword(req, res) {
    try {
      // 验证必填字段
      const requiredFields = ['username', 'newPassword', 'captcha'];
      const validation = this.validateFields(req.body, requiredFields);
      if (!validation.valid) {
        return this.fail(res, '用户名、新密码和验证码不能为空', 400);
      }
      
      const { username, newPassword, captcha } = req.body;
      
      // 验证新密码长度
      if (newPassword.length < 6 || newPassword.length > 20) {
        return this.fail(res, '新密码长度应为6-20个字符', 400);
      }
      
      // 查询用户信息
      try {
        const users = await this.prisma.$queryRaw`
          SELECT id, phone, status FROM "base"."mall_user" 
          WHERE ("username" = ${username} OR "phone" = ${username})
          AND "deleted_at" IS NULL
        `;
        
        if (!users || users.length === 0) {
          return this.fail(res, '用户不存在', 400);
        }
        
        const user = users[0];
        
        // 检查用户状态
        if (user.status !== 1) {
          return this.fail(res, '账号已被禁用，请联系客服', 403);
        }
        
        const phone = user.phone;
        const userId = user.id;
        
        // 验证验证码
        try {
          console.log('开始验证验证码，手机号:', phone, '验证码:', captcha);
          
          // 检查Redis连接状态
          console.log('Redis连接状态:', redisUtil.isConnected ? '已连接' : '未连接');
          
          // 直接从 Redis 中获取验证码进行比较
          const codeKey = `verification_code:forget_password:${phone}`;
          console.log('验证码键:', codeKey);
          
          const savedCode = await redisUtil.getClient().get(codeKey);
          console.log('Redis中存储的验证码:', savedCode || '无');
          
          if (savedCode) {
            console.log('验证码比较:');
            console.log(`- 输入验证码: ${captcha}, 类型: ${typeof captcha}, 长度: ${captcha.length}`);
            console.log(`- 存储验证码: ${savedCode}, 类型: ${typeof savedCode}, 长度: ${savedCode.length}`);
            
            if (savedCode !== captcha) {
              console.log('验证码不匹配');
              return this.fail(res, '验证码错误', 400);
            }
            
            console.log('验证码验证成功');
            
            // 验证成功后删除验证码
            await redisUtil.getClient().del(codeKey);
          } else {
            console.log('验证码不存在或已过期');
            return this.fail(res, '验证码不存在或已过期，请重新获取', 400);
          }
        } catch (error) {
          console.error('验证验证码失败:', error);
          return this.fail(res, '验证验证码失败: ' + error.message, 500);
        }
        
        // 加密新密码
        const salt = await bcrypt.genSalt(10);
        const hashedPassword = await bcrypt.hash(newPassword, salt);
        
        // 更新密码
        const now = Date.now();
        await this.prisma.$executeRaw`
          UPDATE "base"."mall_user"
          SET "password" = ${hashedPassword}, "updated_at" = ${now}
          WHERE "id" = ${userId}
        `;
        
        // 强制用户下线，将其令牌加入黑名单
        await redisUtil.forceUserLogout(userId.toString());
        
        return this.success(res, {}, '密码重置成功，请重新登录');
      } catch (error) {
        console.error('重置密码失败:', error);
        return this.fail(res, '重置密码失败，请稍后再试', 500);
      }
    } catch (error) {
      console.error('重置密码过程中出错:', error);
      return this.fail(res, '重置密码失败，请稍后再试', 500);
    }
  }
}

// 创建控制器实例并传入共享的 prisma 实例
const { prisma } = require('../../../core/database/prisma');
const controller = new UserAuthController(prisma);

// 绑定所有方法的 this 上下文
Object.getOwnPropertyNames(UserAuthController.prototype).forEach(method => {
  if (method !== 'constructor' && typeof controller[method] === 'function') {
    controller[method] = controller[method].bind(controller);
  }
});

module.exports = controller;
