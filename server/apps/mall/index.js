/**
 * 商城前端模块
 * 负责商城前端用户的注册、登录等功能
 */
const express = require('express');
const path = require('path');
const BaseModule = require('../../core/module/BaseModule');

class MallModule extends BaseModule {
  constructor(config, moduleManager) {
    super(config, moduleManager);
    this.router = express.Router();
  }

  /**
   * 初始化模块
   */
  async init() {
    // 注册路由
    await this.registerRoutes();
    
    // 注册 Swagger 文档
    this.moduleManager.emit('swagger:register', {
      module: this.config.name,
      paths: [
        path.join(this.config.path, 'swagger/schemas/*.js'),
        path.join(this.config.path, 'routes/**/*.js')
      ]
    });
  }

  /**
   * 注册模块路由
   */
  async registerRoutes() {
    try {
      // 加载用户路由
      const userRoutes = require('./routes/UserRoute');
      // 加载用户收货地址路由
      const userAddressRoutes = require('./routes/UserAddressRoute');
      // 加载订单路由
      const orderRoutes = require('./routes/OrderRoute');
      // 加载商品路由
      const goodsRoutes = require('./routes/GoodsRoute');
      // 加载分类品牌路由
      const categoryBrandRoutes = require('./routes/CategoryBrandRoute');
      // 加载IP地址查询路由
      const ipLocationRoutes = require('./routes/IpLocationRoute');
      // 加载微信公众号路由
      const WechatRoute = require('./routes/WechatRoute');
      const wechatRoute = new WechatRoute();
      // 加载运费计算路由
      const freightCalculationRoutes = require('./routes/FreightCalculationRoute');
      // 加载用户收藏路由
      const userFavoriteRoutes = require('./routes/UserFavoriteRoute');
      // 加载订单支付路由
      const orderPaymentRoutes = require('./routes/OrderPaymentRoute');
      // 加载订单评价路由
      const orderReviewRoutes = require('./routes/OrderReviewRoute');
      // 加载浏览记录路由
      const browseHistoryRoutes = require('./routes/BrowseHistoryRoute');
      // 加载用户消息路由
      const userMessageRoutes = require('./routes/userMessageRoutes');
      // 加载新闻路由
      const newsRoutes = require('./routes/NewsRoute');
      // 加载发票抬头路由
      const invoiceHeaderRoutes = require('./routes/InvoiceHeaderRoute');
      // 加载轮播图路由
      const bannerRoutes = require('./routes/BannerRoute');

      // 获取共享的 Prisma 客户端实例
      const { prisma } = require('../../core/database/prisma');
      
      // 注册用户路由
      this.router.use('/user', userRoutes(prisma));
      // 注册用户收货地址路由
      this.router.use('/user', userAddressRoutes);
      // 注册订单路由
      this.router.use('/order', orderRoutes(prisma));
      // 注册商品路由
      this.router.use('/goods', goodsRoutes(prisma));
      // 注册分类品牌路由
      this.router.use('/categorybrands', categoryBrandRoutes(prisma));
      // 注册IP地址查询路由
      this.router.use('/ip', ipLocationRoutes);
      // 注册微信公众号路由
      this.router.use('/wechat', wechatRoute.getRouter());
      // 注册运费计算路由
      this.router.use('/freight-calculation', freightCalculationRoutes(prisma));
      // 注册用户收藏路由
      this.router.use('/favorite', userFavoriteRoutes(prisma));
      // 注册订单支付路由
      this.router.use('/order-payment', orderPaymentRoutes(prisma));
      // 注册浏览记录路由
      this.router.use('/browse-history', browseHistoryRoutes(prisma));
      // 注册订单评价路由
      this.router.use('/review', orderReviewRoutes(prisma));
      // 注册用户消息路由
      this.router.use('/user', userMessageRoutes(prisma));
      // 注册新闻路由
      this.router.use('/news', newsRoutes(prisma));
      // 注册发票抬头路由
      this.router.use('/invoice-headers', invoiceHeaderRoutes);
      // 注册轮播图路由
      this.router.use('/banners', bannerRoutes(prisma));

      console.log(`[${this.config.name}] 路由注册成功`);
    } catch (err) {
      console.error(`[${this.config.name}] 路由注册失败:`, err);
      throw err;
    }
  }

  /**
   * 清理模块资源
   */
  async cleanup() {
    console.log(`[${this.config.name}] 模块资源清理完成`);
  }
}

module.exports = MallModule;
