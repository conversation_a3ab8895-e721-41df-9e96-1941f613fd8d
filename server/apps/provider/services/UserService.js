/**
 * 服务商用户服务类
 */
const { prisma } = require('../../../core/database/prisma');
const { Snowflake } = require('nodejs-snowflake');

class UserService {
  constructor() {
    this.prisma = prisma;
    // 配置雪花ID生成器
    this.idGenerator = new Snowflake({
      custom_epoch: 1609459200000, // 2021-01-01
      instance_id: 10 // 服务商模块实例ID
    });
  }

  /**
   * 生成用户ID (雪花算法)
   * @returns {BigInt} - 生成的用户ID
   */
  async generateUserId() {
    return BigInt(this.idGenerator.getUniqueID());
  }
}

module.exports = new UserService();
