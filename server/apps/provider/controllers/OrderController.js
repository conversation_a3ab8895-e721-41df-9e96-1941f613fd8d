/**
 * 服务商订单控制器
 * 负责处理服务商订单相关的请求和响应
 */
const ProviderBaseController = require('./ProviderBaseController');

class OrderController extends ProviderBaseController {
  /**
   * 构造函数
   * @param {Object} prisma - Prisma客户端实例
   */
  constructor(prisma) {
    super();
    if (!prisma) {
      throw new Error('Prisma client is required');
    }
    this.prisma = prisma;
    console.log('OrderController initialized with prisma:', !!this.prisma);
  }

  /**
   * 获取服务商订单列表
   * 只返回当前登录服务商用户跟进的订单
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getOrders(req, res) {
    try {
      // 获取当前用户ID
      const userId = req.user?.id ? BigInt(req.user.id) : null;
      if (!userId) {
        return this.fail(res, '用户未登录', 401);
      }

      // 获取查询参数
      const page = parseInt(req.query.page) || 1;
      const pageSize = parseInt(req.query.pageSize) || 10;
      const sortField = req.query.sortField || 'created_at';
      const sortOrder = req.query.sortOrder || 'desc';

      // 构建过滤条件
      const filters = {};

      // 订单编号筛选
      if (req.query.orderNumber) {
        filters.orderNumber = req.query.orderNumber;
      }

      // 第三方订单编号筛选
      if (req.query.thirdPartyOrderSn) {
        filters.thirdPartyOrderSn = req.query.thirdPartyOrderSn;
      }

      // 订单状态筛选
      if (req.query.orderStatus !== undefined && req.query.orderStatus !== '') {
        filters.orderStatus = parseInt(req.query.orderStatus);
      }

      // 支付状态筛选
      if (req.query.paymentStatus !== undefined && req.query.paymentStatus !== '') {
        filters.paymentStatus = parseInt(req.query.paymentStatus);
      }

      // 订单来源筛选
      if (req.query.orderSource !== undefined && req.query.orderSource !== '') {
        filters.orderSource = parseInt(req.query.orderSource);
      }

      // 收货人姓名筛选
      if (req.query.receiverName) {
        filters.receiverName = req.query.receiverName;
      }

      // 时间范围筛选
      if (req.query.startTime && req.query.endTime) {
        filters.startTime = new Date(req.query.startTime).getTime();
        filters.endTime = new Date(req.query.endTime).getTime();
      }

      // 计算分页偏移量
      const skip = (page - 1) * pageSize;

      // 构建查询条件
      const whereCondition = {
        deleted_at: null, // 未删除的订单
        order_followers: {
          some: {
            follower_id: userId, // 当前用户跟进的订单
            deleted_at: null
          }
        }
      };

      // 添加过滤条件
      if (filters.orderNumber) {
        whereCondition.id = BigInt(filters.orderNumber);
      }

      if (filters.thirdPartyOrderSn) {
        whereCondition.third_party_order_sn = {
          contains: filters.thirdPartyOrderSn
        };
      }

      if (filters.orderStatus !== undefined) {
        whereCondition.order_status = filters.orderStatus;
      }

      if (filters.paymentStatus !== undefined) {
        whereCondition.payment_status = filters.paymentStatus;
      }

      if (filters.orderSource !== undefined) {
        whereCondition.order_source = filters.orderSource;
      }

      // 时间范围过滤
      if (filters.startTime && filters.endTime) {
        whereCondition.created_at = {
          gte: BigInt(filters.startTime),
          lte: BigInt(filters.endTime)
        };
      }

      // 收货人姓名过滤（关联查询收货信息表）
      if (filters.receiverName) {
        whereCondition.order_shipping_info = {
          recipient_name: {
            contains: filters.receiverName
          }
        };
      }

      // 构建排序条件
      const orderBy = {};
      orderBy[sortField] = sortOrder;

      // 查询订单总数
      const total = await this.prisma.orders.count({
        where: whereCondition
      });

      // 查询订单列表
      const orders = await this.prisma.orders.findMany({
        where: whereCondition,
        skip,
        take: pageSize,
        orderBy,
        include: {
          order_items: {
            select: {
              id: true,
              product_name: true,
              sku_code: true,
              product_image: true,
              unit_price: true,
              quantity: true,
              total_price: true,
              sku_specifications: true,
              third_party_sku_id: true,
              third_party_product_code: true
            }
          },
          order_shipping_info: {
            select: {
              recipient_name: true,
              recipient_phone: true,
              street_address: true,
              region_path_name: true,
              region_province_id: true,
              region_city_id: true,
              region_district_id: true,
              postal_code: true
            }
          },
          order_followers: {
            where: {
              deleted_at: null,
              follower_id: userId
            },
            select: {
              follower_id: true
            }
          },
          channel: {
            select: {
              id: true,
              name: true,
              icon_url: true
            }
          }
        }
      });

      // 查询所有订单的assignment_status
      const { Prisma } = require('@prisma/client');
      const orderIds = orders.map(order => BigInt(order.id));
      let assignmentStatusMap = {};
      if (orderIds.length > 0) {
        const assignments = await this.prisma.$queryRaw`
          SELECT order_id, assignment_status FROM provider.order_assignment WHERE order_id IN (${Prisma.join(orderIds)}) AND deleted_at IS NULL
        `;
        assignmentStatusMap = assignments.reduce((map, item) => {
          map[item.order_id.toString()] = item.assignment_status;
          return map;
        }, {});
      }

      // 格式化返回数据
      const formattedOrders = orders.map(order => {
        const shippingInfo = order.order_shipping_info || {};
        // assignment_status逻辑
        let assignment_status = 0;
        if (assignmentStatusMap[order.id.toString()] !== undefined) {
          assignment_status = assignmentStatusMap[order.id.toString()];
        }
        return {
          id: order.id.toString(),
          order_id: order.id.toString(),
          assignment_status, // 派单状态字段，紧跟order_id
          third_party_order_sn: order.third_party_order_sn || '',
          order_status: order.order_status,
          payment_status: order.payment_status,
          shipping_status: order.shipping_status,
          total_amount: parseFloat(order.total_amount?.toString() || '0'),
          paid_amount: parseFloat(order.paid_amount?.toString() || '0'),
          payment_method_id: order.payment_method_id,
          payment_method: order.payment_method || '',
          order_source: order.order_source,
          order_type: order.order_type,
          remark: order.remark || '',
          admin_remark: order.admin_remark || '',
          created_at: order.created_at ? Number(order.created_at) : null,
          updated_at: order.updated_at ? Number(order.updated_at) : null,
          paid_at: order.paid_at ? Number(order.paid_at) : null,
          shipped_at: order.shipped_at ? Number(order.shipped_at) : null,
          completed_at: order.completed_at ? Number(order.completed_at) : null,
          cancelled_at: order.cancelled_at ? Number(order.cancelled_at) : null,
          cancel_reason: order.cancel_reason || '',

          // 收货信息 - 组合完整地址
          receiver_name: shippingInfo.recipient_name || '',
          receiver_phone: shippingInfo.recipient_phone || '',
          receiver_address: shippingInfo.region_path_name && shippingInfo.street_address
            ? `${shippingInfo.region_path_name} ${shippingInfo.street_address}`
            : shippingInfo.street_address || shippingInfo.region_path_name || '',

          // 渠道信息
          channel_id: order.channel_id,
          channel_name: order.channel?.name || '',
          channel_icon_url: order.channel?.icon_url || '',

          // 商品信息
          products: order.order_items.map(item => ({
            id: item.id.toString(),
            product_name: item.product_name,
            product_sku: item.sku_code || '',
            sku: item.sku_code || '',
            third_sku: item.third_party_sku_id || '',
            product_image: item.product_image || '',
            price: parseFloat(item.unit_price?.toString() || '0'),
            quantity: item.quantity,
            subtotal: parseFloat(item.total_price?.toString() || '0'),
            spec: item.sku_specifications ? JSON.stringify(item.sku_specifications) : '',
            vendor: '',
            third_party_product_code: item.third_party_product_code || ''
          })),

          // 跟进人信息
          followers: order.order_followers.map(follower => ({
            follower_id: follower.follower_id.toString()
          }))
        };
      });

      // 返回结果，code为200
      return res.json({
        code: 200,
        data: {
          items: formattedOrders,
          pageInfo: {
            total,
            currentPage: page,
            totalPage: Math.ceil(total / pageSize)
          }
        },
        message: '获取订单列表成功'
      });

    } catch (error) {
      console.error('获取服务商订单列表失败:', error);
      return this.fail(res, `获取订单列表失败: ${error.message}`, 500);
    }
  }

  /**
   * 获取指派给当前服务商的订单列表
   * 基于provider.order_assignment表筛选订单
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getProviderOrders(req, res) {
    try {
      // 获取当前用户ID
      const userId = req.user?.id ? BigInt(req.user.id) : null;
      if (!userId) {
        return this.fail(res, '用户未登录', 401);
      }

      // 获取查询参数
      const page = parseInt(req.query.page) || 1;
      const pageSize = parseInt(req.query.pageSize) || 10;
      const sortField = req.query.sortField || 'created_at';
      const sortOrder = req.query.sortOrder || 'desc';

      // 构建过滤条件
      const filters = {};

      // 订单编号筛选
      if (req.query.orderNumber) {
        filters.orderNumber = req.query.orderNumber;
      }

      // 第三方订单编号筛选
      if (req.query.thirdPartyOrderSn) {
        filters.thirdPartyOrderSn = req.query.thirdPartyOrderSn;
      }

      // 订单状态筛选
      if (req.query.orderStatus !== undefined && req.query.orderStatus !== '') {
        filters.orderStatus = parseInt(req.query.orderStatus);
      }

      // 支付状态筛选
      if (req.query.paymentStatus !== undefined && req.query.paymentStatus !== '') {
        filters.paymentStatus = parseInt(req.query.paymentStatus);
      }

      // 订单来源筛选
      if (req.query.orderSource !== undefined && req.query.orderSource !== '') {
        filters.orderSource = parseInt(req.query.orderSource);
      }

      // 收货人姓名筛选
      if (req.query.receiverName) {
        filters.receiverName = req.query.receiverName;
      }

      // 时间范围筛选
      if (req.query.startTime && req.query.endTime) {
        filters.startTime = new Date(req.query.startTime).getTime();
        filters.endTime = new Date(req.query.endTime).getTime();
      }

      // 计算分页偏移量
      const skip = (page - 1) * pageSize;

      // 构建查询条件 - 基于provider.order_assignment表
      const whereCondition = {
        deleted_at: null, // 未删除的订单
      };

      // 添加与provider.order_assignment表的关联条件
      // 使用原生SQL查询来获取符合条件的订单ID
      const orderAssignmentCondition = `
        SELECT order_id FROM provider.order_assignment 
        WHERE provider_id = ${userId.toString()} AND deleted_at IS NULL
      `;

      const orderIds = await this.prisma.$queryRaw`
        SELECT order_id FROM provider.order_assignment 
        WHERE provider_id = ${userId} AND deleted_at IS NULL
      `;

      if (!orderIds || orderIds.length === 0) {
        // 没有分配的订单
        return this.success(res, {
          items: [],
          pageInfo: {
            total: 0,
            currentPage: page,
            totalPage: 0
          }
        }, '获取服务商订单列表成功');
      }

      // 将查询到的订单ID添加到where条件
      whereCondition.id = {
        in: orderIds.map(item => item.order_id)
      };

      // 添加其他过滤条件
      if (filters.orderNumber) {
        whereCondition.id = BigInt(filters.orderNumber);
      }

      if (filters.thirdPartyOrderSn) {
        whereCondition.third_party_order_sn = {
          contains: filters.thirdPartyOrderSn
        };
      }

      if (filters.orderStatus !== undefined) {
        whereCondition.order_status = filters.orderStatus;
      }

      if (filters.paymentStatus !== undefined) {
        whereCondition.payment_status = filters.paymentStatus;
      }

      if (filters.orderSource !== undefined) {
        whereCondition.order_source = filters.orderSource;
      }

      // 时间范围过滤
      if (filters.startTime && filters.endTime) {
        whereCondition.created_at = {
          gte: BigInt(filters.startTime),
          lte: BigInt(filters.endTime)
        };
      }

      // 收货人姓名过滤（关联查询收货信息表）
      if (filters.receiverName) {
        whereCondition.order_shipping_info = {
          recipient_name: {
            contains: filters.receiverName
          }
        };
      }

      // 构建排序条件
      const orderBy = {};
      orderBy[sortField] = sortOrder;

      // 查询订单总数
      const total = await this.prisma.orders.count({
        where: whereCondition
      });

      // 查询订单列表
      const orders = await this.prisma.orders.findMany({
        where: whereCondition,
        skip,
        take: pageSize,
        orderBy,
        include: {
          order_items: {
            select: {
              id: true,
              product_name: true,
              sku_code: true,
              product_image: true,
              unit_price: true,
              quantity: true,
              total_price: true,
              sku_specifications: true,
              third_party_sku_id: true,
              third_party_product_code: true
            }
          },
          order_shipping_info: {
            select: {
              recipient_name: true,
              recipient_phone: true,
              street_address: true,
              region_path_name: true,
              region_province_id: true,
              region_city_id: true,
              region_district_id: true,
              postal_code: true
            }
          },
          channel: {
            select: {
              id: true,
              name: true,
              icon_url: true
            }
          }
        }
      });

      // 获取order_assignment表中的数据
      const orderIdList = orders.map(order => order.id);

      let orderAssignments = [];

      // 使用一个更简单的查询方法，不通过IN子句
      if (orderIdList.length > 0) {
        // 对每个订单单独查询其对应的assignment
        const assignments = await Promise.all(
          orderIdList.map(orderId =>
            this.prisma.$queryRaw`
              SELECT * FROM provider.order_assignment 
              WHERE provider_id = ${userId} 
              AND order_id = ${orderId}
              AND deleted_at IS NULL
              LIMIT 1
            `
          )
        );

        // 合并结果
        orderAssignments = assignments.flat().filter(a => a);
      }

      // 构建订单ID到订单分配信息的映射
      const orderAssignmentMap = {};
      for (const assignment of orderAssignments) {
        orderAssignmentMap[assignment.order_id] = assignment;
      }

      // 格式化返回数据
      const formattedOrders = orders.map(order => {
        const shippingInfo = order.order_shipping_info || {};
        const assignment = orderAssignmentMap[order.id];

        return {
          id: order.id.toString(),
          order_id: order.id.toString(), // 系统订单编号
          third_party_order_sn: order.third_party_order_sn || '', // 第三方订单编号
          order_status: order.order_status,
          payment_status: order.payment_status,
          shipping_status: order.shipping_status,
          total_amount: parseFloat(order.total_amount?.toString() || '0'),
          paid_amount: parseFloat(order.paid_amount?.toString() || '0'),
          payment_method_id: order.payment_method_id,
          payment_method: order.payment_method || '', // 直接使用订单表中的支付方式名称
          order_source: order.order_source,
          order_type: order.order_type,
          remark: order.remark || '',
          admin_remark: order.admin_remark || '',
          created_at: order.created_at ? Number(order.created_at) : null,
          updated_at: order.updated_at ? Number(order.updated_at) : null,
          paid_at: order.paid_at ? Number(order.paid_at) : null,
          shipped_at: order.shipped_at ? Number(order.shipped_at) : null,
          completed_at: order.completed_at ? Number(order.completed_at) : null,
          cancelled_at: order.cancelled_at ? Number(order.cancelled_at) : null,
          cancel_reason: order.cancel_reason || '',

          // 收货信息 - 组合完整地址
          receiver_name: shippingInfo.recipient_name || '',
          receiver_phone: shippingInfo.recipient_phone || '',
          receiver_address: shippingInfo.region_path_name && shippingInfo.street_address
            ? `${shippingInfo.region_path_name} ${shippingInfo.street_address}`
            : shippingInfo.street_address || shippingInfo.region_path_name || '',

          // 渠道信息
          channel_id: order.channel_id,
          channel_name: order.channel?.name || '',
          channel_icon_url: order.channel?.icon_url || '',

          // 商品信息
          products: order.order_items.map(item => ({
            id: item.id.toString(),
            product_name: item.product_name,
            product_sku: item.sku_code || '',
            sku: item.sku_code || '',
            third_sku: item.third_party_sku_id || '',
            product_image: item.product_image || '',
            price: parseFloat(item.unit_price?.toString() || '0'),
            quantity: item.quantity,
            subtotal: parseFloat(item.total_price?.toString() || '0'),
            spec: item.sku_specifications ? JSON.stringify(item.sku_specifications) : '',
            vendor: '', // 商家主体信息需要额外查询
            third_party_product_code: item.third_party_product_code || ''
          })),

          // 订单分配信息
          assignment: assignment ? {
            id: assignment.id.toString(),
            provider_id: assignment.provider_id.toString(),
            order_report_id: assignment.order_report_id.toString(),
            assignment_status: assignment.assignment_status,
            rate: parseFloat(assignment.rate?.toString() || '0'),
            assignment_amount: parseFloat(assignment.assignment_amount?.toString() || '0'),
            assigned_at: assignment.assigned_at ? Number(assignment.assigned_at) : null,
            accepted_at: assignment.accepted_at ? Number(assignment.accepted_at) : null,
            completed_at: assignment.completed_at ? Number(assignment.completed_at) : null,
            remark: assignment.remark || ''
          } : null
        };
      });

      // 返回结果
      return this.success(res, {
        items: formattedOrders,
        pageInfo: {
          total,
          currentPage: page,
          totalPage: Math.ceil(total / pageSize)
        }
      }, '获取服务商订单列表成功');

    } catch (error) {
      console.error('获取服务商订单列表失败:', error);
      return this.fail(res, `获取订单列表失败: ${error.message}`, 500);
    }
  }

  /**
   * 获取订单详情
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getOrderDetail(req, res) {
    try {
      // 获取当前用户ID
      const userId = req.user?.id ? BigInt(req.user.id) : null;
      if (!userId) {
        return this.fail(res, '用户未登录', 401);
      }

      // 优先从路径参数获取订单ID，如果没有则从查询参数获取
      let orderIdParam = req.params.id || req.query.id;
      if (!orderIdParam) {
        return this.fail(res, '订单ID不能为空', 400);
      }

      let orderId;
      try {
        orderId = BigInt(orderIdParam);
      } catch (error) {
        return this.fail(res, '订单ID格式无效', 400);
      }

      // 查询订单详情，确保只能查看自己跟进的订单
      const order = await this.prisma.orders.findFirst({
        where: {
          id: orderId,
          deleted_at: null,
          order_followers: {
            some: {
              follower_id: userId,
              deleted_at: null
            }
          }
        },
        include: {
          order_items: true,
          order_shipping_info: true,
          order_followers: {
            where: { deleted_at: null }
          },
          channel: true,
          order_logs: {
            orderBy: { created_at: 'desc' }
          }
        }
      });

      if (!order) {
        return this.fail(res, '订单不存在或无权限查看', 404);
      }

      // 格式化返回数据（类似列表接口的格式化逻辑）
      const shippingInfo = order.order_shipping_info || {};

      const formattedOrder = {
        id: order.id.toString(),
        order_id: order.id.toString(),
        third_party_order_sn: order.third_party_order_sn || '',
        order_status: order.order_status,
        payment_status: order.payment_status,
        shipping_status: order.shipping_status,
        total_amount: parseFloat(order.total_amount?.toString() || '0'),
        paid_amount: parseFloat(order.paid_amount?.toString() || '0'),
        payment_method_id: order.payment_method_id,
        payment_method: order.payment_method || '', // 直接使用订单表中的支付方式名称
        order_source: order.order_source,
        order_type: order.order_type,
        remark: order.remark || '',
        admin_remark: order.admin_remark || '',
        created_at: order.created_at ? Number(order.created_at) : null,
        updated_at: order.updated_at ? Number(order.updated_at) : null,
        paid_at: order.paid_at ? Number(order.paid_at) : null,
        shipped_at: order.shipped_at ? Number(order.shipped_at) : null,
        completed_at: order.completed_at ? Number(order.completed_at) : null,
        cancelled_at: order.cancelled_at ? Number(order.cancelled_at) : null,
        cancel_reason: order.cancel_reason || '',

        // 收货信息 - 组合完整地址
        receiver_name: shippingInfo.recipient_name || '',
        receiver_phone: shippingInfo.recipient_phone || '',
        receiver_address: shippingInfo.region_path_name && shippingInfo.street_address
          ? `${shippingInfo.region_path_name} ${shippingInfo.street_address}`
          : shippingInfo.street_address || shippingInfo.region_path_name || '',

        // 渠道信息
        channel_id: order.channel_id,
        channel_name: order.channel?.name || '',
        channel_icon_url: order.channel?.icon_url || '',

        // 商品信息
        products: order.order_items.map(item => ({
          id: item.id.toString(),
          product_name: item.product_name,
          product_sku: item.sku_code || '',
          sku: item.sku_code || '',
          third_sku: item.third_party_sku_id || '',
          product_image: item.product_image || '',
          price: parseFloat(item.unit_price || 0),
          quantity: item.quantity,
          subtotal: parseFloat(item.total_price || 0),
          spec: item.sku_specifications ? JSON.stringify(item.sku_specifications) : '',
          vendor: '',
          third_party_product_code: item.third_party_product_code || ''
        })),

        // 跟进人信息
        followers: order.order_followers.map(follower => ({
          follower_id: follower.follower_id.toString()
        })),

        // 订单日志
        logs: order.order_logs.map(log => ({
          id: log.id.toString(),
          action: log.action,
          description: log.description,
          created_at: log.created_at,
          created_by: log.created_by?.toString() || ''
        }))
      };

      return this.success(res, formattedOrder, '获取订单详情成功');

    } catch (error) {
      console.error('获取订单详情失败:', error);
      return this.fail(res, `获取订单详情失败: ${error.message}`, 500);
    }
  }

  /**
   * 服务商接单
   * 更新order_assignment表中的assignment_status和accepted_at字段
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async receiveOrder(req, res) {
    try {
      // 获取当前用户ID
      const userId = req.user?.id ? BigInt(req.user.id) : null;
      if (!userId) {
        return this.fail(res, '用户未登录', 401);
      }

      // 获取订单ID
      const { id } = req.body;
      if (!id) {
        return this.fail(res, '订单ID不能为空', 400);
      }

      let orderId;
      try {
        orderId = BigInt(id);
      } catch (error) {
        return this.fail(res, '订单ID格式无效', 400);
      }

      // 查询订单分配记录
      const assignments = await this.prisma.$queryRaw`
        SELECT * FROM provider.order_assignment 
        WHERE provider_id = ${userId} 
        AND order_id = ${orderId}
        AND deleted_at IS NULL
        LIMIT 1
      `;

      // 检查是否找到记录
      if (!assignments || assignments.length === 0) {
        return this.fail(res, '订单分配记录不存在或无权限接单', 404);
      }

      const assignment = assignments[0];

      // 检查订单状态是否允许接单
      if (assignment.assignment_status !== 1) { // 假设状态1是待接单
        return this.fail(res, '当前订单状态不允许接单', 400);
      }

      // 更新接单状态
      const currentTime = BigInt(Date.now());
      await this.prisma.$queryRaw`
        UPDATE provider.order_assignment 
        SET 
          assignment_status = 2, 
          accepted_at = ${currentTime},
          updated_at = ${currentTime}
        WHERE id = ${assignment.id}
      `;

      // 新增：更新base.orders表的order_status为1
      await this.prisma.$executeRaw`
        UPDATE base.orders SET order_status = 1 WHERE id = ${orderId}
      `;

      // 返回成功结果
      return this.success(res, {
        id: assignment.id.toString(),
        order_id: orderId.toString()
      }, '接单成功');
    } catch (error) {
      console.error('服务商接单失败:', error);
      return this.fail(res, `接单失败: ${error.message}`, 500);
    }
  }

  /**
   * 订单退回
   * 更新order_assignment表中的assignment_status、completed_at和deleted_at字段
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async rollbackOrder(req, res) {
    try {
      // 获取当前用户ID
      const userId = req.user?.id ? BigInt(req.user.id) : null;
      if (!userId) {
        return this.fail(res, '用户未登录', 401);
      }

      // 获取订单ID和备注
      const { id, remark } = req.body;
      if (!id) {
        return this.fail(res, '订单ID不能为空', 400);
      }

      let orderId;
      try {
        orderId = BigInt(id);
      } catch (error) {
        return this.fail(res, '订单ID格式无效', 400);
      }

      // 查询订单分配记录
      const assignments = await this.prisma.$queryRaw`
        SELECT * FROM provider.order_assignment 
        WHERE provider_id = ${userId} 
        AND order_id = ${orderId}
        AND deleted_at IS NULL
        LIMIT 1
      `;

      // 检查是否找到记录
      if (!assignments || assignments.length === 0) {
        return this.fail(res, '订单分配记录不存在或无权限操作', 404);
      }

      const assignment = assignments[0];

      // 检查订单状态是否允许退回
      if (assignment.assignment_status === 3) {
        return this.fail(res, '订单已经被退回，请勿重复操作', 400);
      }

      // 更新订单状态
      const currentTime = BigInt(Date.now());
      await this.prisma.$queryRaw`
        UPDATE provider.order_assignment 
        SET 
          assignment_status = 3, 
          completed_at = ${currentTime},
          deleted_at = ${currentTime},
          remark = ${remark || '用户主动退回订单'},
          updated_at = ${currentTime}
        WHERE id = ${assignment.id}
      `;

      // 新增：更新base.orders表的order_status为0
      await this.prisma.$executeRaw`
        UPDATE base.orders SET order_status = 0 WHERE id = ${orderId}
      `;

      // 返回成功结果
      return this.success(res, {
        id: assignment.id.toString(),
        order_id: orderId.toString()
      }, '订单退回成功');
    } catch (error) {
      console.error('订单退回失败:', error);
      return this.fail(res, `订单退回失败: ${error.message}`, 500);
    }
  }

  async getProviderOrdersList(req, res) {
    try {
      // 获取当前用户ID
      const userId = req.user?.id ? BigInt(req.user.id) : null;
      if (!userId) {
        return this.fail(res, '用户未登录', 401);
      }

      // 获取查询参数
      const page = parseInt(req.query.page) || 1;
      const pageSize = parseInt(req.query.pageSize) || 10;
      const sortField = req.query.sortField || 'created_at';
      const sortOrder = req.query.sortOrder || 'desc';

      // 构建过滤条件
      const filters = {};

      if (req.query.orderNumber) {
        filters.orderNumber = req.query.orderNumber;
      }
      if (req.query.thirdPartyOrderSn) {
        filters.thirdPartyOrderSn = req.query.thirdPartyOrderSn;
      }
      if (req.query.orderStatus !== undefined && req.query.orderStatus !== '') {
        filters.orderStatus = parseInt(req.query.orderStatus);
      }
      if (req.query.paymentStatus !== undefined && req.query.paymentStatus !== '') {
        filters.paymentStatus = parseInt(req.query.paymentStatus);
      }
      if (req.query.orderSource !== undefined && req.query.orderSource !== '') {
        filters.orderSource = parseInt(req.query.orderSource);
      }
      if (req.query.receiverName) {
        filters.receiverName = req.query.receiverName;
      }
      if (req.query.startTime && req.query.endTime) {
        filters.startTime = new Date(req.query.startTime).getTime();
        filters.endTime = new Date(req.query.endTime).getTime();
      }

      const skip = (page - 1) * pageSize;
      const whereCondition = { deleted_at: null };

      // 这里SQL用salesman_id
      const orderAssignmentCondition = `
        SELECT order_id FROM provider.order_assignment 
        WHERE salesman_id = ${userId.toString()} AND deleted_at IS NULL
      `;

      const orderIds = await this.prisma.$queryRaw`
        SELECT order_id FROM provider.order_assignment 
        WHERE salesman_id = ${userId} AND deleted_at IS NULL
      `;

      if (!orderIds || orderIds.length === 0) {
        return this.success(res, {
          items: [],
          pageInfo: {
            total: 0,
            currentPage: page,
            totalPage: 0
          }
        }, '获取服务商订单列表成功');
      }

      whereCondition.id = {
        in: orderIds.map(item => item.order_id)
      };

      if (filters.orderNumber) {
        whereCondition.id = BigInt(filters.orderNumber);
      }
      if (filters.thirdPartyOrderSn) {
        whereCondition.third_party_order_sn = {
          contains: filters.thirdPartyOrderSn
        };
      }
      if (filters.orderStatus !== undefined) {
        whereCondition.order_status = filters.orderStatus;
      }
      if (filters.paymentStatus !== undefined) {
        whereCondition.payment_status = filters.paymentStatus;
      }
      if (filters.orderSource !== undefined) {
        whereCondition.order_source = filters.orderSource;
      }
      if (filters.startTime && filters.endTime) {
        whereCondition.created_at = {
          gte: BigInt(filters.startTime),
          lte: BigInt(filters.endTime)
        };
      }
      if (filters.receiverName) {
        whereCondition.order_shipping_info = {
          recipient_name: {
            contains: filters.receiverName
          }
        };
      }

      const orderBy = {};
      orderBy[sortField] = sortOrder;

      const total = await this.prisma.orders.count({ where: whereCondition });
      const orders = await this.prisma.orders.findMany({
        where: whereCondition,
        skip,
        take: pageSize,
        orderBy,
        include: {
          order_items: {
            select: {
              id: true,
              product_name: true,
              sku_code: true,
              product_image: true,
              unit_price: true,
              quantity: true,
              total_price: true,
              sku_specifications: true,
              third_party_sku_id: true,
              third_party_product_code: true
            }
          },
          order_shipping_info: {
            select: {
              recipient_name: true,
              recipient_phone: true,
              street_address: true,
              region_path_name: true,
              region_province_id: true,
              region_city_id: true,
              region_district_id: true,
              postal_code: true
            }
          },
          channel: {
            select: {
              id: true,
              name: true,
              icon_url: true
            }
          }
        }
      });

      const orderIdList = orders.map(order => order.id);
      let orderAssignments = [];
      if (orderIdList.length > 0) {
        const assignments = await Promise.all(
          orderIdList.map(orderId =>
            this.prisma.$queryRaw`
              SELECT * FROM provider.order_assignment 
              WHERE salesman_id = ${userId} 
              AND order_id = ${orderId}
              AND deleted_at IS NULL
              LIMIT 1
            `
          )
        );
        orderAssignments = assignments.map(a => a && a[0] ? a[0] : null);
      }

      const formattedOrders = orders.map((order, idx) => {
        const shippingInfo = order.order_shipping_info || {};
        const assignment = orderAssignments[idx] || {};
        return {
          id: order.id.toString(),
          order_id: order.id.toString(),
          third_party_order_sn: order.third_party_order_sn || '',
          order_status: order.order_status,
          payment_status: order.payment_status,
          total_amount: parseFloat(order.total_amount?.toString() || '0'),
          paid_amount: parseFloat(order.paid_amount?.toString() || '0'),
          receiver_name: shippingInfo.recipient_name || '',
          receiver_phone: shippingInfo.recipient_phone || '',
          receiver_address: shippingInfo.region_path_name && shippingInfo.street_address
            ? `${shippingInfo.region_path_name} ${shippingInfo.street_address}`
            : shippingInfo.street_address || shippingInfo.region_path_name || '',
          products: order.order_items.map(item => ({
            product_name: item.product_name,
            price: parseFloat(item.unit_price || 0),
            quantity: item.quantity
          })),
          assignment: assignment ? {
            id: assignment.id?.toString() || '',
            salesman_id: assignment.salesman_id?.toString() || '',
            assignment_status: assignment.assignment_status,
            rate: assignment.rate,
            assignment_amount: assignment.assignment_amount,
            assigned_at: assignment.assigned_at ? Number(assignment.assigned_at) : null
          } : {},
          created_at: order.created_at ? Number(order.created_at) : null
        };
      });

      const totalPage = Math.ceil(total / pageSize);
      return this.success(res, {
        items: formattedOrders,
        pageInfo: {
          total,
          currentPage: page,
          totalPage
        }
      }, '获取服务商订单列表成功');
    } catch (error) {
      console.error('获取服务商订单列表失败:', error);
      return this.fail(res, '获取服务商订单列表失败', 500);
    }
  }
}

module.exports = OrderController;
