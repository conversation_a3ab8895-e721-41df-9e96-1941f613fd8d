/**
 * 爬虫日志模型
 */
const { PrismaClient } = require('@prisma/client');
const { generateSnowflakeId } = require('../../../shared/utils/snowflake');
const prisma = new PrismaClient();

class SpiderLogModel {
  /**
   * 创建爬虫执行日志
   * @param {Object} data 日志数据
   * @returns {Promise<Object>} 创建的日志
   */
  static async create(data) {
    const now = Date.now();
    
    // 从输入数据中提取任务ID
    const { task_id, ...restData } = data;
    
    // 确保有ID字段，如果没有则生成一个雪花ID
    const id = data.id ? BigInt(data.id) : generateSnowflakeId();
    
    // 准备符合Prisma模型的数据结构
    const createData = {
      id,
      ...restData,
      // 使用task关联而不是task_id字段
      task: {
        connect: { id: BigInt(task_id) }
      },
      created_at: now,
      updated_at: now
    };
    
    // 确保数字字段为数字类型
    if (createData.status !== undefined) {
      createData.status = Number(createData.status);
    }
    if (createData.items_count !== undefined) {
      createData.items_count = Number(createData.items_count);
    }
    
    // 确保BigInt类型字段正确转换
    if (createData.start_time && typeof createData.start_time === 'string') {
      createData.start_time = BigInt(createData.start_time);
    }
    if (createData.end_time && typeof createData.end_time === 'string') {
      createData.end_time = BigInt(createData.end_time);
    }
    if (createData.created_by && typeof createData.created_by === 'string') {
      createData.created_by = BigInt(createData.created_by);
    }
    if (createData.spider_id && typeof createData.spider_id === 'string') {
      createData.spider_id = BigInt(createData.spider_id);
    }
    if (createData.store_id && typeof createData.store_id === 'string') {
      createData.store_id = BigInt(createData.store_id);
    }
    
    try {
      return prisma.spiderLog.create({
        data: createData
      });
    } catch (error) {
      console.error('创建爬虫日志失败:', error);
      throw error;
    }
  }

  /**
   * 更新爬虫执行日志
   * @param {number} id 日志ID
   * @param {Object} data 更新数据
   * @returns {Promise<Object>} 更新后的日志
   */
  static async update(id, data) {
    return prisma.spiderLog.update({
      where: { id },
      data: {
        ...data,
        updated_at: Date.now()
      }
    });
  }

  /**
   * 获取爬虫执行日志详情
   * @param {number} id 日志ID
   * @returns {Promise<Object>} 日志详情
   */
  static async findById(id) {
    return prisma.spiderLog.findFirst({
      where: {
        id,
        deleted_at: null
      },
      include: {
        task: true
      }
    });
  }

  /**
   * 分页查询爬虫执行日志
   * @param {Object} params 查询参数
   * @param {number} params.page 页码
   * @param {number} params.pageSize 每页数量
   * @param {number} params.taskId 任务ID（可选）
   * @param {number} params.storeId 店铺ID（可选）
   * @param {number} params.status 状态（可选）
   * @param {number} params.startTime 开始时间（可选）
   * @param {number} params.endTime 结束时间（可选）
   * @returns {Promise<Object>} 分页结果
   */
  static async findByPage(params) {
    const { page = 1, pageSize = 10, taskId, storeId, status, startTime, endTime } = params;
    const skip = (page - 1) * pageSize;
    
    // 构建查询条件
    const where = {
      deleted_at: null
    };

    if (taskId) {
      where.task_id = taskId;
    }

    if (storeId) {
      where.store_id = BigInt(storeId);
    }

    if (status !== undefined) {
      where.status = status;
    }
    
    if (startTime) {
      where.start_time = {
        gte: startTime
      };
    }
    
    if (endTime) {
      where.start_time = {
        ...(where.start_time || {}),
        lte: endTime
      };
    }
    
    // 查询总数
    const total = await prisma.spiderLog.count({ where });
    
    // 查询数据
    const list = await prisma.spiderLog.findMany({
      where,
      skip,
      take: pageSize,
      orderBy: {
        created_at: 'desc'
      },
      include: {
        task: {
          select: {
            spider_id: true,
            platform_id: true,
            store_id: true,
            spider_type: true
          }
        }
      }
    });
    
    return {
      list,
      pagination: {
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize)
      }
    };
  }
  
  /**
   * 获取任务的最近执行日志
   * @param {number} taskId 任务ID
   * @param {number} limit 限制数量
   * @returns {Promise<Array>} 日志列表
   */
  static async findRecentByTaskId(taskId, limit = 10) {
    return prisma.spiderLog.findMany({
      where: {
        task_id: taskId,
        deleted_at: null
      },
      orderBy: {
        start_time: 'desc'
      },
      take: limit
    });
  }
  
  /**
   * 统计任务执行情况
   * @param {number} taskId 任务ID
   * @returns {Promise<Object>} 统计结果
   */
  static async getTaskStats(taskId) {
    const stats = await prisma.$queryRaw`
      SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as success,
        SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as failed,
        SUM(items_count) as total_items
      FROM spider.spider_log
      WHERE task_id = ${taskId} AND deleted_at IS NULL
    `;
    
    return stats[0] || { total: 0, success: 0, failed: 0, total_items: 0 };
  }
}

module.exports = SpiderLogModel;
