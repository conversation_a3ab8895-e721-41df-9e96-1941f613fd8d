/**
 * 爬虫模型
 */
const { prisma } = require('../../../core/database/prisma');
const { generateSnowflakeId } = require('../../../shared/utils/snowflake');

/**
 * 检查数据库表是否存在
 * @returns {Promise<boolean>}
 */
async function checkTableExists() {
  try {
    // 尝试查询一条数据，如果表不存在会抛出异常
    await prisma.spider.findFirst();
    return true;
  } catch (error) {
    if (error.message.includes('does not exist')) {
      console.warn('爬虫表不存在，尝试自动创建数据库表');
      try {
        await createSpiderTable();
        console.log('爬虫表创建成功');
        return true;
      } catch (createError) {
        console.error('创建爬虫表失败:', createError);
        return false;
      }
    }
    // 如果是其他错误，重新抛出
    throw error;
  }
}

/**
 * 创建爬虫表
 * @returns {Promise<void>}
 */
async function createSpiderTable() {
  try {
    // 创建schema
    await prisma.$executeRaw`CREATE SCHEMA IF NOT EXISTS spider;`;
    
    // 创建爬虫表
    await prisma.$executeRaw`
      CREATE TABLE IF NOT EXISTS spider.spider (
        id BIGINT PRIMARY KEY,
        platform_id BIGINT NOT NULL,
        name VARCHAR(100) NOT NULL,
        code VARCHAR(50) NOT NULL,
        spider_type VARCHAR(255),
        remark TEXT,
        status SMALLINT NOT NULL DEFAULT 1,
        created_by BIGINT,
        updated_by BIGINT,
        created_at BIGINT NOT NULL DEFAULT ((EXTRACT(epoch FROM now()) * 1000)::bigint),
        updated_at BIGINT NOT NULL DEFAULT ((EXTRACT(epoch FROM now()) * 1000)::bigint),
        deleted_at BIGINT
      );
    `;
    
    // 创建索引
    await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS spider_code_idx ON spider.spider(code);`;
    await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS spider_status_idx ON spider.spider(status);`;
    await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS spider_platform_id_idx ON spider.spider(platform_id);`;
    
    console.log('爬虫表和索引创建成功');
  } catch (error) {
    console.error('创建爬虫表时出错:', error);
    throw new Error(`创建爬虫表失败: ${error.message}`);
  }
}

/**
 * 处理文本字段，确保不超出数据库限制
 * @param {string} text 文本内容
 * @returns {string} 处理后的文本
 */
function processTextField(text) {
  if (!text) return null;
  
  try {
    // 确保字符串不超过65KB (PostgreSQL Text类型的安全限制)
    const maxLength = 65535;
    if (text.length > maxLength) {
      console.warn(`文本数据过大(${text.length}字节)，已截断至${maxLength}字节`);
      return text.substring(0, maxLength);
    }
    
    return text;
  } catch (error) {
    console.error('处理文本字段时出错:', error);
    return '';
  }
}

class SpiderModel {
  /**
   * 创建爬虫
   * @param {Object} data 爬虫数据
   * @returns {Promise<Object>} 创建的爬虫
   */
  static async create(data) {
    try {
      const tableExists = await checkTableExists();
      if (!tableExists) {
        throw new Error('爬虫表不存在，无法创建爬虫');
      }
      
      const id = generateSnowflakeId();
      const now = Date.now();
      
      // 处理remark字段，确保不超出数据库限制
      const processedData = { ...data };
      if (processedData.remark) {
        processedData.remark = processTextField(processedData.remark);
      }
      
      // 处理platform_id字段，确保是BigInt类型
      if (!processedData.platform_id) {
        throw new Error('平台ID是必填项');
      }
      
      try {
        processedData.platform_id = BigInt(processedData.platform_id);
      } catch (error) {
        console.error(`平台ID格式错误 (${processedData.platform_id}):`, error.message);
        throw new Error(`无法将平台ID转换为BigInt: ${error.message}`);
      }
      
      return await prisma.spider.create({
        data: {
          id,
          ...processedData,
          created_at: now,
          updated_at: now
        }
      });
    } catch (error) {
      console.error('创建爬虫失败:', error.message);
      throw error;
    }
  }

  /**
   * 更新爬虫
   * @param {BigInt|string} id 爬虫ID
   * @param {Object} data 更新的数据
   * @returns {Promise<Object>} 更新后的爬虫
   */
  static async update(id, data) {
    try {
      const now = Date.now();
      
      // 处理remark字段，确保不超出数据库限制
      const processedData = { ...data };
      if (processedData.remark) {
        processedData.remark = processTextField(processedData.remark);
      }
      
      // 处理platform_id字段，确保是BigInt类型
      if (processedData.platform_id) {
        try {
          processedData.platform_id = BigInt(processedData.platform_id);
        } catch (error) {
          console.error(`平台ID格式错误 (${processedData.platform_id}):`, error.message);
          throw new Error(`无法将平台ID转换为BigInt: ${error.message}`);
        }
      }
      
      // 确保ID是有效的格式并转换为BigInt
      let parsedId;
      try {
        // 如果ID是JSON字符串，先解析它
        if (typeof id === 'string' && (id.startsWith('{') || id.includes('"id":'))) {
          const parsed = JSON.parse(id);
          parsedId = BigInt(parsed.id || id);
        } else {
          // 否则直接转换
          parsedId = BigInt(id);
        }
      } catch (parseError) {
        console.error(`ID格式错误 (${id}):`, parseError.message);
        throw new Error(`无法将ID转换为BigInt: ${parseError.message}`);
      }
      
      return await prisma.spider.update({
        where: { id: parsedId },
        data: {
          ...processedData,
          updated_at: now
        }
      });
    } catch (error) {
      console.error(`更新爬虫失败 (ID: ${id}):`, error.message);
      throw error;
    }
  }

  /**
   * 删除爬虫（软删除）
   * @param {BigInt|string} id 爬虫ID
   * @returns {Promise<Object>} 删除结果
   */
  static async delete(id) {
    try {
      const now = Date.now();
      
      // 确保ID是有效的格式并转换为BigInt
      let parsedId;
      try {
        // 如果ID是JSON字符串，先解析它
        if (typeof id === 'string' && (id.startsWith('{') || id.includes('"id":'))) {
          const parsed = JSON.parse(id);
          parsedId = BigInt(parsed.id || id);
        } else {
          // 否则直接转换
          parsedId = BigInt(id);
        }
      } catch (parseError) {
        console.error(`ID格式错误 (${id}):`, parseError.message);
        throw new Error(`无法将ID转换为BigInt: ${parseError.message}`);
      }
      
      return await prisma.spider.update({
        where: { id: parsedId },
        data: {
          deleted_at: now
        }
      });
    } catch (error) {
      console.error(`删除爬虫失败 (ID: ${id}):`, error.message);
      throw error;
    }
  }

  /**
   * 获取爬虫详情
   * @param {BigInt|string} id 爬虫ID
   * @returns {Promise<Object|null>} 爬虫详情
   */
  static async findById(id) {
    try {
      // 确保ID是有效的格式并转换为BigInt
      let parsedId;
      try {
        // 如果ID是JSON字符串，先解析它
        if (typeof id === 'string' && (id.startsWith('{') || id.includes('"id":'))) {
          const parsed = JSON.parse(id);
          parsedId = BigInt(parsed.id || id);
        } else {
          // 否则直接转换
          parsedId = BigInt(id);
        }
      } catch (parseError) {
        console.error(`ID格式错误 (${id}):`, parseError.message);
        throw new Error(`无法将ID转换为BigInt: ${parseError.message}`);
      }
      
      return await prisma.spider.findFirst({
        where: {
          id: parsedId,
          deleted_at: null
        }
      });
    } catch (error) {
      console.error(`获取爬虫详情失败 (ID: ${id}):`, error.message);
      throw error;
    }
  }

  /**
   * 获取爬虫列表
   * @param {Object} params 查询参数
   * @param {number} params.page 页码
   * @param {number} params.pageSize 每页数量
   * @param {string} params.name 爬虫名称（模糊查询）
   * @param {string} params.code 爬虫代码（精确查询）
   * @param {string} params.spider_type 爬虫类型（order-订单，goods-商品，invoice-发票，report-报表）
   * @param {number} params.status 状态（1-启用，0-禁用）
   * @returns {Promise<Object>} 爬虫列表和总数
   */
  static async findAll(params = {}) {
    try {
      const { page = 1, pageSize = 10, name, code, status, spider_type, platform_id } = params;
      
      const where = {
        deleted_at: null
      };
      
      if (name) {
        where.name = {
          contains: name
        };
      }
      
      if (code) {
        where.code = code;
      }
      
      if (spider_type) {
        where.spider_type = spider_type;
      }
      
      if (status !== undefined) {
        where.status = parseInt(status);
      }
      
      if (platform_id) {
        try {
          where.platform_id = BigInt(platform_id);
        } catch (error) {
          console.error(`平台ID格式错误 (${platform_id}):`, error.message);
          throw new Error(`无法将平台ID转换为BigInt: ${error.message}`);
        }
      }
      
      const [total, items] = await Promise.all([
        prisma.spider.count({ where }),
        prisma.spider.findMany({
          where,
          skip: (page - 1) * pageSize,
          take: pageSize,
          orderBy: {
            created_at: 'desc'
          }
        })
      ]);
      
      return {
        total,
        items
      };
    } catch (error) {
      console.error('获取爬虫列表失败:', error.message);
      throw error;
    }
  }

  /**
   * 根据代码获取爬虫
   * @param {string} code 爬虫代码
   * @returns {Promise<Object|null>} 爬虫详情
   */
  static async findByCode(code, platform_id = null) {
    try {
      const where = {
        code,
        deleted_at: null
      };
      
      if (platform_id) {
        try {
          where.platform_id = BigInt(platform_id);
        } catch (error) {
          console.error(`平台ID格式错误 (${platform_id}):`, error.message);
          throw new Error(`无法将平台ID转换为BigInt: ${error.message}`);
        }
      }
      
      return await prisma.spider.findFirst({
        where
      });
    } catch (error) {
      console.error(`根据代码获取爬虫失败 (code: ${code}):`, error.message);
      throw error;
    }
  }
  
  /**
   * 检查在指定平台下爬虫类型是否已存在
   * @param {BigInt} platform_id 平台ID
   * @param {string} spider_type 爬虫类型
   * @param {BigInt} excludeId 排除的爬虫ID（用于更新时检查）
   * @returns {Promise<boolean>} 是否存在
   */
  static async isTypeExistsInPlatform(platform_id, spider_type, excludeId = null) {
    try {
      if (!platform_id || !spider_type) {
        return false;
      }
      
      const where = {
        platform_id: BigInt(platform_id),
        spider_type,
        deleted_at: null
      };
      
      if (excludeId) {
        where.id = {
          not: BigInt(excludeId)
        };
      }
      
      const count = await prisma.spider.count({ where });
      return count > 0;
    } catch (error) {
      console.error(`检查平台下爬虫类型是否存在失败 (platform_id: ${platform_id}, spider_type: ${spider_type}):`, error.message);
      throw error;
    }
  }
  
  /**
   * 检查爬虫代码是否存在
   * @param {string} code 爬虫代码
   * @param {BigInt} excludeId 排除的爬虫ID（用于更新时检查）
   * @param {BigInt} platform_id 平台ID
   * @returns {Promise<boolean>} 是否存在
   */
  static async isCodeExists(code, excludeId = null, platform_id = null) {
    try {
      const where = {
        code,
        deleted_at: null
      };
      
      if (excludeId) {
        try {
          where.id = {
            not: BigInt(excludeId)
          };
        } catch (error) {
          console.error(`ID格式错误 (${excludeId}):`, error.message);
          throw new Error(`无法将ID转换为BigInt: ${error.message}`);
        }
      }
      
      if (platform_id) {
        try {
          where.platform_id = BigInt(platform_id);
        } catch (error) {
          console.error(`平台ID格式错误 (${platform_id}):`, error.message);
          throw new Error(`无法将平台ID转换为BigInt: ${error.message}`);
        }
      }
      
      const count = await prisma.spider.count({ where });
      return count > 0;
    } catch (error) {
      console.error(`检查爬虫代码是否存在失败 (code: ${code}):`, error.message);
      throw error;
    }
  }
}

module.exports = SpiderModel;
