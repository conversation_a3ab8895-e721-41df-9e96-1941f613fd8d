/**
 * 爬虫任务模型
 */
const { prisma } = require('../../../core/database/prisma');
const { generateSnowflakeId } = require('../../../shared/utils/snowflake');

/**
 * 检查数据库表是否存在
 * @returns {Promise<boolean>}
 */
async function checkTableExists() {
  try {
    // 尝试查询一条数据，如果表不存在会抛出异常
    await prisma.spiderTask.findFirst();
    return true;
  } catch (error) {
    if (error.message.includes('does not exist')) {
      console.warn('爬虫任务表不存在，请先创建数据库表');
      return false;
    }
    // 如果是其他错误，重新抛出
    throw error;
  }
}

class SpiderTaskModel {
  /**
   * 创建爬虫任务
   * @param {Object} data 任务数据
   * @returns {Promise<Object>} 创建的任务
   */
  static async create(data) {
    try {
      const tableExists = await checkTableExists();
      if (!tableExists) {
        throw new Error('爬虫任务表不存在，无法创建任务');
      }
      
      // 处理必填字段和类型转换
      const { 
        platform, // 从输入中移除不存在的字段
        created_by,
        ...restData 
      } = data;
      
      // 生成雪花ID
      const id = BigInt(Date.now());
      
      // 准备任务数据
      const taskData = {
        id,
        ...data
      };
      
      // 确保数字字段为数字类型
      if (taskData.status !== undefined) {
        taskData.status = Number(taskData.status);
      }
      if (taskData.timeout !== undefined) {
        taskData.timeout = Number(taskData.timeout);
      }
      if (taskData.retry_count !== undefined) {
        taskData.retry_count = Number(taskData.retry_count);
      }
      if (taskData.priority !== undefined) {
        taskData.priority = Number(taskData.priority);
      }
      
      // 确保 BigInt 类型字段正确转换
      if (taskData.id && typeof taskData.id === 'string') {
        taskData.id = BigInt(taskData.id);
      }
      if (taskData.spider_id && typeof taskData.spider_id === 'string') {
        taskData.spider_id = BigInt(taskData.spider_id);
      }
      if (taskData.platform_id && typeof taskData.platform_id === 'string') {
        taskData.platform_id = BigInt(taskData.platform_id);
      }
      if (taskData.store_id && typeof taskData.store_id === 'string') {
        taskData.store_id = BigInt(taskData.store_id);
      }
      if (taskData.created_by && typeof taskData.created_by === 'string') {
        taskData.created_by = BigInt(taskData.created_by);
      }
      
      // 创建爬虫任务
      const task = await prisma.spiderTask.create({
        data: taskData
      });
      
      return task;
    } catch (error) {
      console.error('创建爬虫任务失败:', error.message);
      throw error;
    }
  }

  /**
   * 更新爬虫任务
   * @param {number|string} id 任务ID
   * @param {Object} data 更新数据
   * @returns {Promise<Object>} 更新后的任务
   */
  static async update(id, data) {
    try {
      const tableExists = await checkTableExists();
      if (!tableExists) {
        throw new Error('爬虫任务表不存在，无法更新任务');
      }
      
      // 处理必填字段和类型转换
      const { 
        platform, // 从输入中移除不存在的字段
        created_by,
        updated_by,
        ...restData 
      } = data;
      
      // 准备更新数据
      const updateData = {
        ...restData,
        updated_at: BigInt(Date.now())
      };
      
      // 处理特殊字段
      if (data.platform_id !== undefined) {
        updateData.platform_id = BigInt(data.platform_id);
      }
      
      if (data.updated_by !== undefined) {
        updateData.updated_by = data.updated_by ? BigInt(data.updated_by) : null;
      }
      
      // 确保ID是有效的格式并转换为BigInt
      let parsedId;
      try {
        // 如果ID是JSON字符串，先解析它
        if (typeof id === 'string' && (id.startsWith('{') || id.includes('"id":'))) {
          const parsed = JSON.parse(id);
          parsedId = BigInt(parsed.id || id);
        } else {
          // 否则直接转换
          parsedId = BigInt(id);
        }
      } catch (parseError) {
        console.error(`ID格式错误 (${id}):`, parseError.message);
        throw new Error(`无法将ID转换为BigInt: ${parseError.message}`);
      }
      
      return await prisma.spiderTask.update({
        where: { id: parsedId },
        data: updateData
      });
    } catch (error) {
      console.error(`更新爬虫任务(ID: ${id})失败:`, error.message);
      throw error;
    }
  }

  /**
   * 删除爬虫任务
   * @param {number} id 任务ID
   * @returns {Promise<Object>} 删除结果
   */
  static async delete(id) {
    try {
      const tableExists = await checkTableExists();
      if (!tableExists) {
        throw new Error('爬虫任务表不存在，无法删除任务');
      }
      
      return await prisma.spiderTask.update({
        where: { id },
        data: {
          deleted_at: Date.now()
        }
      });
    } catch (error) {
      console.error(`删除爬虫任务(ID: ${id})失败:`, error.message);
      throw error;
    }
  }

  /**
   * 根据ID查找爬虫任务
   * @param {number|string} id 任务ID
   * @returns {Promise<Object>} 任务对象
   */
  static async findById(id) {
    try {
      const tableExists = await checkTableExists();
      if (!tableExists) {
        console.warn(`爬虫任务表不存在，无法查询任务(ID: ${id})`);
        return null;
      }
      
      // 确保ID是有效的格式并转换为BigInt
      let parsedId;
      try {
        // 如果ID是JSON字符串，先解析它
        if (typeof id === 'string' && (id.startsWith('{') || id.includes('"id":'))) {
          const parsed = JSON.parse(id);
          parsedId = BigInt(parsed.id || id);
        } else {
          // 否则直接转换
          parsedId = BigInt(id);
        }
      } catch (parseError) {
        console.error(`ID格式错误 (${id}):`, parseError.message);
        return null; // 返回null而不是抛出异常，与原函数行为一致
      }
      
      return await prisma.spiderTask.findFirst({
        where: {
          id: parsedId,
          deleted_at: null
        }
      });
    } catch (error) {
      console.error(`查询爬虫任务(ID: ${id})失败:`, error.message);
      return null;
    }
  }

  /**
   * 分页查询爬虫任务
   * @param {Object} params 查询参数
   * @param {number} params.page 页码
   * @param {number} params.pageSize 每页数量
   * @param {string} params.spiderId 爬虫ID（可选）
   * @param {string} params.platformId 平台ID（可选）
   * @param {string} params.storeId 店铺ID（可选）
   * @param {string} params.spiderType 爬虫类型（可选）
   * @returns {Promise<Object>} 分页结果
   */
  static async findByPage(params) {
    try {
      console.log('SpiderTaskModel.findByPage - 开始查询，参数:', params);
      
      const tableExists = await checkTableExists();
      console.log('SpiderTaskModel.findByPage - 表是否存在:', tableExists);
      
      if (!tableExists) {
        console.warn('爬虫任务表不存在，返回空列表');
        return {
          list: [],
          pagination: {
            page: params.page,
            pageSize: params.pageSize,
            total: 0,
            totalPages: 0
          }
        };
      }
      
      const { page = 1, pageSize = 10, spiderId, platformId, storeId, spiderType } = params;
      const skip = (page - 1) * pageSize;
      
      // 构建查询条件
      const where = {
        deleted_at: null
      };
      
      if (spiderId) {
        where.spider_id = BigInt(spiderId);
        console.log('SpiderTaskModel.findByPage - 添加爬虫ID过滤:', spiderId);
      }
      
      if (platformId) {
        where.platform_id = BigInt(platformId);
        console.log('SpiderTaskModel.findByPage - 添加平台ID过滤:', platformId);
      }
      
      if (storeId) {
        where.store_id = BigInt(storeId);
        console.log('SpiderTaskModel.findByPage - 添加店铺ID过滤:', storeId);
      }
      
      if (spiderType) {
        where.spider_type = spiderType;
        console.log('SpiderTaskModel.findByPage - 添加爬虫类型过滤:', spiderType);
      }
      
      console.log('SpiderTaskModel.findByPage - 最终查询条件:', JSON.stringify(where, (key, value) => 
        typeof value === 'bigint' ? value.toString() : value
      ));
      
      // 查询总数
      const total = await prisma.spiderTask.count({ where });
      console.log('SpiderTaskModel.findByPage - 查询总数结果:', total);
      
      // 查询数据
      const list = await prisma.spiderTask.findMany({
        where,
        skip,
        take: pageSize,
        orderBy: {
          updated_at: 'desc'
        }
      });
      
      console.log('SpiderTaskModel.findByPage - 查询列表结果数量:', list?.length || 0);
      
      return {
        list,
        pagination: {
          page,
          pageSize,
          total,
          totalPages: Math.ceil(total / pageSize)
        }
      };
    } catch (error) {
      console.error('查询爬虫任务列表失败:', error.message);
      return {
        list: [],
        pagination: {
          page: params.page,
          pageSize: params.pageSize,
          total: 0,
          totalPages: 0
        }
      };
    }
  }

  /**
   * 检查是否已存在相同组合的爬虫任务
   * @param {Object} params 参数对象
   * @param {BigInt|string} params.spider_id 爬虫ID
   * @param {BigInt|string} params.platform_id 平台ID
   * @param {BigInt|string} params.store_id 店铺ID
   * @param {string} params.spider_type 爬虫类型
   * @returns {Promise<boolean>} 是否存在
   */
  static async checkCombinationExists(params) {
    try {
      const tableExists = await checkTableExists();
      if (!tableExists) {
        return false;
      }
      
      const { spider_id, platform_id, store_id, spider_type } = params;
      
      console.log('检查爬虫任务组合是否存在:', {
        spider_id,
        platform_id,
        store_id,
        spider_type
      });
      
      // 构建查询条件
      const where = {
        deleted_at: null
      };
      
      if (spider_id) {
        where.spider_id = BigInt(spider_id);
      }
      
      if (platform_id) {
        where.platform_id = BigInt(platform_id);
      }
      
      if (store_id) {
        where.store_id = BigInt(store_id);
      }
      
      if (spider_type) {
        where.spider_type = spider_type;
      }
      
      // 查询是否存在
      const count = await prisma.spiderTask.count({ where });
      
      console.log('爬虫任务组合查询结果:', count > 0 ? '已存在' : '不存在');
      
      return count > 0;
    } catch (error) {
      console.error('检查爬虫任务组合是否存在失败:', error.message);
      return false;
    }
  }
  
  /**
   * 根据店铺ID查询爬虫任务
   * @param {string|BigInt} storeId 店铺ID
   * @returns {Promise<Array>} 爬虫任务列表
   */
  static async findByStoreId(storeId) {
    try {
      const tableExists = await checkTableExists();
      if (!tableExists) {
        console.warn('爬虫任务表不存在，返回空列表');
        return [];
      }
      
      // 构建查询条件
      const where = {
        store_id: BigInt(storeId),
        deleted_at: null
      };
      
      // 查询数据
      const tasks = await prisma.spiderTask.findMany({
        where,
        orderBy: {
          updated_at: 'desc'
        }
      });
      
      return tasks;
    } catch (error) {
      console.error(`根据店铺ID(${storeId})查询爬虫任务失败:`, error.message);
      return [];
    }
  }
  
  /**
   * 根据条件查询爬虫任务列表
   * @param {Object} query 查询条件
   * @param {number} page 页码
   * @param {number} pageSize 每页数量
   * @returns {Promise<Object>} 分页结果
   */
  static async findByQuery(query = {}, page = 1, pageSize = 10) {
    try {
      const tableExists = await checkTableExists();
      if (!tableExists) {
        console.warn('爬虫任务表不存在，返回空列表');
        return {
          list: [],
          pagination: {
            page,
            pageSize,
            total: 0
          }
        };
      }
      
      const where = {
        deleted_at: null
      };
      
      // 添加查询条件
      if (query.name) {
        where.name = {
          contains: query.name
        };
      }
      
      if (query.status !== undefined) {
        where.status = parseInt(query.status);
      }
      
      // 查询总数
      const total = await prisma.spiderTask.count({ where });
      
      // 查询数据
      const list = await prisma.spiderTask.findMany({
        where,
        skip: (page - 1) * pageSize,
        take: pageSize,
        orderBy: {
          updated_at: 'desc'
        }
      });
      
      return {
        list,
        pagination: {
          page,
          pageSize,
          total
        }
      };
    } catch (error) {
      console.error('查询爬虫任务列表失败:', error.message);
      return {
        list: [],
        pagination: {
          page,
          pageSize,
          total: 0
        }
      };
    }
  }

  /**
   * 获取所有启用的爬虫任务
   * @returns {Promise<Array>} 任务列表
   */
  static async findAllEnabled() {
    try {
      const tableExists = await checkTableExists();
      if (!tableExists) {
        console.warn('爬虫任务表不存在，返回空数组');
        return [];
      }

      // 移除 status 字段查询条件，因为 SpiderTask 表中没有该字段
      // 只保留 deleted_at 为 null 的条件，表示未删除的任务
      return await prisma.spiderTask.findMany({
        where: {
          deleted_at: null
        }
      });
    } catch (error) {
      // 如果表不存在或查询失败，返回空数组
      console.warn('爬虫任务表不存在或查询失败，返回空数组:', error.message);
      return [];
    }
  }

  /**
   * 根据开启自动同步的店铺查询爬虫任务
   * @returns {Promise<Array>} 任务列表
   */
  static async findByAutoSyncStores() {
    try {
      const tableExists = await checkTableExists();
      if (!tableExists) {
        console.warn('爬虫任务表不存在，返回空数组');
        return [];
      }

      // 使用原生SQL查询，关联店铺表
      const query = `
        SELECT st.*
        FROM spider.spider_task st
        INNER JOIN base.store s ON st.store_id = s.id
        WHERE st.deleted_at IS NULL
          AND s.deleted_at IS NULL
          AND s.status = 1
          AND s.is_auto_sync = 1
        ORDER BY st.updated_at DESC
      `;

      const tasks = await prisma.$queryRawUnsafe(query);

      console.log(`查询到 ${tasks.length} 个开启自动同步的店铺关联的爬虫任务`);
      return tasks;
    } catch (error) {
      console.error('根据自动同步店铺查询爬虫任务失败:', error.message);
      return [];
    }
  }
}

module.exports = SpiderTaskModel;