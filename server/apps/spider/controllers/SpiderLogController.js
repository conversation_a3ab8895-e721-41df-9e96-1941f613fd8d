/**
 * 爬虫日志控制器
 */
const SpiderLogModel = require('../models/SpiderLogModel');
const SpiderTaskModel = require('../models/SpiderTaskModel');

class SpiderLogController {
  /**
   * 获取爬虫日志列表
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async list(req, res) {
    try {
      const { page = 1, pageSize = 10, storeId, status, startTime, endTime } = req.query;
      const result = await SpiderLogModel.findByPage({
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        storeId: storeId ? BigInt(storeId) : undefined,
        status: status !== undefined ? parseInt(status) : undefined,
        startTime: startTime ? parseInt(startTime) : undefined,
        endTime: endTime ? parseInt(endTime) : undefined
      });
      
      res.json({
        code: 200,
        data: result.list,
        pagination: result.pagination,
        message: '获取爬虫日志列表成功'
      });
    } catch (error) {
      console.error('获取爬虫日志列表失败:', error);
      res.status(500).json({
        code: 500,
        message: '获取爬虫日志列表失败: ' + error.message
      });
    }
  }
  
  /**
   * 获取爬虫日志详情
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async detail(req, res) {
    try {
      const { id } = req.params;
      
      const log = await SpiderLogModel.findById(parseInt(id));
      if (!log) {
        return res.status(404).json({
          code: 404,
          message: '爬虫日志不存在'
        });
      }
      
      res.json({
        code: 200,
        data: log,
        message: '获取爬虫日志详情成功'
      });
    } catch (error) {
      console.error('获取爬虫日志详情失败:', error);
      res.status(500).json({
        code: 500,
        message: '获取爬虫日志详情失败: ' + error.message
      });
    }
  }
  
  /**
   * 获取指定任务的爬虫日志列表
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async listByTask(req, res) {
    try {
      const { taskId } = req.params;
      const { page = 1, pageSize = 10, status } = req.query;
      
      // 检查任务是否存在
      const task = await SpiderTaskModel.findById(parseInt(taskId));
      if (!task) {
        return res.status(404).json({
          code: 404,
          message: '爬虫任务不存在'
        });
      }
      
      const result = await SpiderLogModel.findByPage({
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        taskId: parseInt(taskId),
        status: status !== undefined ? parseInt(status) : undefined
      });
      
      res.json({
        code: 200,
        data: result.list,
        pagination: result.pagination,
        message: '获取任务爬虫日志列表成功'
      });
    } catch (error) {
      console.error('获取任务爬虫日志列表失败:', error);
      res.status(500).json({
        code: 500,
        message: '获取任务爬虫日志列表失败: ' + error.message
      });
    }
  }
}

module.exports = new SpiderLogController();
