/**
 * 爬虫调度管理服务
 * 负责管理爬虫任务的定时调度
 */
const config = require('../config');
const SpiderTaskModel = require('../models/SpiderTaskModel');
const SpiderExecutorService = require('./SpiderExecutorService');
const { TRIGGER_TYPE } = require('../constants');

class ScheduleManagerService {
  constructor() {
    this.scheduledTasks = new Map(); // 存储所有调度任务，key 为任务 ID，value 为调度信息
    this.runningTasks = new Set(); // 存储正在执行的任务 ID
    this.initialized = false;
    this.checkIntervalId = null; // 存储检查间隔的定时器 ID
  }
  
  /**
   * 初始化调度管理器
   */
  async initialize() {
    if (this.initialized) {
      return;
    }
    
    console.log('初始化爬虫调度管理器...');
    
    try {

      // 加载开启自动同步的店铺关联的爬虫任务
      const autoSyncTasks = await SpiderTaskModel.findByAutoSyncStores();
      console.log(`发现${autoSyncTasks.length}个开启自动同步的店铺关联的爬虫任务`);

      // 为每个任务创建调度
      for (const task of autoSyncTasks) {
        await this.scheduleTask(task);
      }
      
      // 启动定时检查
      this.startPeriodicCheck();
      
      this.initialized = true;
      console.log('爬虫调度管理器初始化完成');
    } catch (error) {
      console.error('初始化爬虫调度管理器失败:', error);
    }
  }
  
  /**
   * 计算下次执行时间
   * @param {Object} task 爬虫任务
   * @returns {number|null} 下次执行时间戳（毫秒）
   */
  calculateNextRunTime(task) {
    if (!task) {
      return null;
    }
    
    try {
      // 优先使用 run_interval 字段（秒）
      if (task.run_interval && task.run_interval > 0) {
        return Date.now() + (task.run_interval * 1000);
      }
      
      // 如果没有 run_interval，尝试使用 cron_expression
      if (task.cron_expression) {
        // 这里简化处理，实际项目中应使用 cron 解析库
        // 例如 node-cron 或 cron-parser
        // 这里暂时返回当前时间加一小时
        return Date.now() + 3600000;
      }
      
      return null;
    } catch (error) {
      console.error('计算下次执行时间失败:', error);
      return null;
    }
  }
  
  /**
   * 更新任务的下次执行时间
   * @param {Object} task 爬虫任务
   * @returns {Promise<Object>} 更新后的任务
   */
  async updateTaskNextRunTime(task) {
    const nextRunTime = this.calculateNextRunTime(task);
    if (nextRunTime) {
      return SpiderTaskModel.update(task.id, { next_run_time: nextRunTime });
    }
    
    return task;
  }
  
  /**
   * 调度任务
   * @param {Object} task 爬虫任务
   * @returns {Promise<void>}
   */
  async scheduleTask(task) {
    try {
      // 如果任务已经被调度，先取消之前的调度
      if (this.scheduledTasks.has(task.id.toString())) {
        this.cancelTask(task.id.toString());
      }
      
      // 计算下次执行时间
      const nextRunTime = this.calculateNextRunTime(task);
      if (!nextRunTime) {
        console.warn(`任务 ${task.id} 没有有效的执行时间配置`);
        return;
      }
      
      // 更新任务的下次执行时间
      await SpiderTaskModel.update(task.id, { next_run_time: nextRunTime });
      
      // 创建调度信息
      const scheduleInfo = {
        taskId: task.id.toString(),
        nextRunTime,
        task: { ...task }
      };
      
      // 存储调度信息
      this.scheduledTasks.set(task.id.toString(), scheduleInfo);
      
      console.log(`任务 ${task.id} 已调度，下次执行时间: ${new Date(nextRunTime).toLocaleString()}`);
    } catch (error) {
      console.error(`调度任务 ${task.id} 失败:`, error);
    }
  }
  
  /**
   * 取消任务调度
   * @param {string} taskId 任务ID
   */
  cancelTask(taskId) {
    if (this.scheduledTasks.has(taskId)) {
      this.scheduledTasks.delete(taskId);
      console.log(`任务 ${taskId} 的调度已取消`);
    }
  }

  /**
   * 添加新任务到调度器（供外部调用）
   * @param {Object} task 爬虫任务
   * @returns {Promise<void>}
   */
  async addTask(task) {
    if (!this.initialized) {
      console.warn('调度管理器未初始化，跳过添加任务');
      return;
    }

    try {
      await this.scheduleTask(task);
      console.log(`新任务 ${task.id} 已添加到调度器`);
    } catch (error) {
      console.error(`添加任务 ${task.id} 到调度器失败:`, error);
    }
  }

  /**
   * 更新调度器中的任务（供外部调用）
   * @param {Object} task 更新后的爬虫任务
   * @returns {Promise<void>}
   */
  async updateTask(task) {
    if (!this.initialized) {
      console.warn('调度管理器未初始化，跳过更新任务');
      return;
    }

    try {
      // 先取消原有调度
      this.cancelTask(task.id.toString());
      // 重新调度任务
      await this.scheduleTask(task);
      console.log(`任务 ${task.id} 的调度已更新`);
    } catch (error) {
      console.error(`更新任务 ${task.id} 的调度失败:`, error);
    }
  }

  /**
   * 从调度器中移除任务（供外部调用）
   * @param {string|BigInt} taskId 任务ID
   */
  removeTask(taskId) {
    if (!this.initialized) {
      console.warn('调度管理器未初始化，跳过移除任务');
      return;
    }

    try {
      this.cancelTask(taskId.toString());
      console.log(`任务 ${taskId} 已从调度器中移除`);
    } catch (error) {
      console.error(`从调度器移除任务 ${taskId} 失败:`, error);
    }
  }

  /**
   * 同步店铺相关的爬虫任务（供外部调用）
   * @param {string|BigInt} storeId 店铺ID
   * @param {boolean} isAutoSync 是否开启自动同步
   * @param {number} status 店铺状态
   * @returns {Promise<void>}
   */
  async syncStoreRelatedTasks(storeId, isAutoSync, status) {
    if (!this.initialized) {
      console.warn('调度管理器未初始化，跳过同步店铺相关任务');
      return;
    }

    try {
      console.log(`同步店铺 ${storeId} 相关任务，自动同步: ${isAutoSync}, 状态: ${status}`);

      // 获取该店铺的所有爬虫任务
      const storeTasks = await SpiderTaskModel.findByStoreId(storeId);

      if (isAutoSync && status === 1) {
        // 店铺开启自动同步且状态正常，添加/更新所有相关任务到调度器
        for (const task of storeTasks) {
          await this.updateTask(task);
        }
        console.log(`已添加/更新店铺 ${storeId} 的 ${storeTasks.length} 个爬虫任务到调度器`);
      } else {
        // 店铺关闭自动同步或状态异常，从调度器移除所有相关任务
        for (const task of storeTasks) {
          this.removeTask(task.id);
        }
        console.log(`已从调度器移除店铺 ${storeId} 的 ${storeTasks.length} 个爬虫任务`);
      }
    } catch (error) {
      console.error(`同步店铺 ${storeId} 相关任务失败:`, error);
    }
  }
  
  /**
   * 启动定时检查
   */
  startPeriodicCheck() {
    // 清除之前的定时器（如果存在）
    if (this.checkIntervalId) {
      clearInterval(this.checkIntervalId);
    }
    
    // 设置新的定时器，定期检查需要执行的任务
    this.checkIntervalId = setInterval(async () => {
      await this.checkScheduledTasks();
    }, config.scheduler.checkInterval || 60000); // 默认每分钟检查一次
    
    console.log(`定时检查已启动，间隔: ${config.scheduler.checkInterval || 60000}ms`);
  }
  
  /**
   * 停止定时检查
   */
  stopPeriodicCheck() {
    if (this.checkIntervalId) {
      clearInterval(this.checkIntervalId);
      this.checkIntervalId = null;
      console.log('定时检查已停止');
    }
  }
  
  /**
   * 检查调度任务
   * @returns {Promise<void>}
   */
  async checkScheduledTasks() {
    const now = Date.now();
    const maxConcurrent = config.scheduler.maxConcurrentTasks || 5;

    try {
      // 首先清理已删除的任务
      await this.cleanupOrphanedTasks();

      // 检查是否有任务需要执行
      for (const [taskId, scheduleInfo] of this.scheduledTasks.entries()) {
        // 如果当前正在运行的任务数量达到最大并发数，跳过检查
        if (this.runningTasks.size >= maxConcurrent) {
          break;
        }

        // 如果任务已经在运行，跳过
        if (this.runningTasks.has(taskId)) {
          continue;
        }

        // 如果到达或超过了下次执行时间，执行任务
        if (scheduleInfo.nextRunTime <= now) {
          // 标记任务为运行中
          this.runningTasks.add(taskId);

          // 异步执行任务
          this.executeTask(scheduleInfo.task)
            .catch(error => console.error(`执行任务 ${taskId} 失败:`, error))
            .finally(() => {
              // 无论成功失败，都从运行中任务集合中移除
              this.runningTasks.delete(taskId);
            });
        }
      }
    } catch (error) {
      console.error('检查调度任务失败:', error);
    }
  }

  /**
   * 清理已删除的孤立任务
   * @returns {Promise<void>}
   */
  async cleanupOrphanedTasks() {
    try {
      const orphanedTaskIds = [];

      // 检查所有已调度的任务是否仍然存在
      for (const [taskId] of this.scheduledTasks.entries()) {
        try {
          const task = await SpiderTaskModel.findById(taskId);
          if (!task) {
            orphanedTaskIds.push(taskId);
          }
        } catch (error) {
          // 如果查询失败，也认为是孤立任务
          console.warn(`检查任务 ${taskId} 存在性失败:`, error.message);
          orphanedTaskIds.push(taskId);
        }
      }

      // 移除孤立任务
      for (const taskId of orphanedTaskIds) {
        console.log(`清理孤立任务: ${taskId}`);
        this.cancelTask(taskId);
      }

      if (orphanedTaskIds.length > 0) {
        console.log(`已清理 ${orphanedTaskIds.length} 个孤立任务`);
      }
    } catch (error) {
      console.error('清理孤立任务失败:', error);
    }
  }
  
  /**
   * 执行任务
   * @param {Object} task 爬虫任务
   * @returns {Promise<void>}
   */
  async executeTask(task) {
    try {
      console.log(`开始执行任务 ${task.id}`);
      const now = Date.now();

      // 首先检查任务是否仍然存在
      const existingTask = await SpiderTaskModel.findById(task.id);
      if (!existingTask) {
        console.warn(`任务 ${task.id} 不存在或已被删除，取消执行并移除调度`);
        this.cancelTask(task.id.toString());
        return;
      }

      // 更新任务的最后执行时间
      await SpiderTaskModel.update(task.id, {
        last_run_time: now
      });

      // 使用爬虫执行服务执行任务
      await SpiderExecutorService.executeSpider(task, task.last_run_time || 0, {
        triggerId: TRIGGER_TYPE.SCHEDULED
      });

      // 重新获取最新的任务信息
      const updatedTask = await SpiderTaskModel.findById(task.id);

      // 如果任务仍然存在，重新调度
      if (updatedTask) {
        await this.scheduleTask(updatedTask);
      } else {
        console.warn(`任务 ${task.id} 执行后不存在，取消调度`);
        this.cancelTask(task.id.toString());
      }

      console.log(`任务 ${task.id} 执行完成`);
    } catch (error) {
      console.error(`执行任务 ${task.id} 失败:`, error);

      // 检查是否是因为记录不存在导致的错误
      if (error.code === 'P2025' || error.message.includes('Record to update not found')) {
        console.warn(`任务 ${task.id} 不存在，取消调度`);
        this.cancelTask(task.id.toString());
        return;
      }

      // 即使失败也重新调度任务（如果任务仍然存在）
      try {
        const updatedTask = await SpiderTaskModel.findById(task.id);
        if (updatedTask) {
          await this.scheduleTask(updatedTask);
        } else {
          console.warn(`任务 ${task.id} 不存在，取消调度`);
          this.cancelTask(task.id.toString());
        }
      } catch (scheduleError) {
        console.error(`重新调度任务 ${task.id} 失败:`, scheduleError);
        // 如果重新调度也失败，可能是任务已被删除，取消调度
        if (scheduleError.code === 'P2025' || scheduleError.message.includes('Record to update not found')) {
          console.warn(`任务 ${task.id} 不存在，取消调度`);
          this.cancelTask(task.id.toString());
        }
      }
    }
  }
}

// 创建单例
const scheduleManager = new ScheduleManagerService();

module.exports = scheduleManager;
