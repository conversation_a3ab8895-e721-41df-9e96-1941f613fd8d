import { $fetch } from 'ofetch'

const BASE_URL = '/api/master/csm/main-products'

/**
 * 主营产品API
 */
export const mainProductApi = {
  /**
   * 获取主营产品列表
   * @param {Object} params 查询参数
   * @returns {Promise} 响应数据
   */
  getList(params = {}) {
    return $fetch(BASE_URL, {
      method: 'GET',
      params
    })
  },

  /**
   * 根据ID获取主营产品详情
   * @param {number} id 主营产品ID
   * @returns {Promise} 响应数据
   */
  getById(id) {
    return $fetch(`${BASE_URL}/${id}`, {
      method: 'GET'
    })
  },

  /**
   * 创建主营产品
   * @param {Object} data 主营产品数据
   * @returns {Promise} 响应数据
   */
  create(data) {
    return $fetch(BASE_URL, {
      method: 'POST',
      body: data
    })
  },

  /**
   * 更新主营产品
   * @param {number} id 主营产品ID
   * @param {Object} data 更新数据
   * @returns {Promise} 响应数据
   */
  update(id, data) {
    return $fetch(`${BASE_URL}/${id}`, {
      method: 'PUT',
      body: data
    })
  },

  /**
   * 删除主营产品
   * @param {number} id 主营产品ID
   * @returns {Promise} 响应数据
   */
  delete(id) {
    return $fetch(`${BASE_URL}/${id}`, {
      method: 'DELETE'
    })
  },

  /**
   * 批量删除主营产品
   * @param {Array<number>} ids 主营产品ID数组
   * @returns {Promise} 响应数据
   */
  batchDelete(ids) {
    return $fetch(`${BASE_URL}/batch-delete`, {
      method: 'POST',
      body: { ids }
    })
  }
}
