import { request } from '@/utils/request.js'
import { apiBaseUrl } from '@/api/config'

/**
 * 主营产品API
 */
export const mainProductApi = {
  /**
   * 获取主营产品列表
   * @param {Object} params 查询参数
   * @returns {Promise} 响应数据
   */
  getList(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/main-products`,
      method: 'get',
      params
    })
  },

  /**
   * 根据ID获取主营产品详情
   * @param {number} id 主营产品ID
   * @returns {Promise} 响应数据
   */
  getById(id) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/main-products/${id}`,
      method: 'get'
    })
  },

  /**
   * 创建主营产品
   * @param {Object} data 主营产品数据
   * @returns {Promise} 响应数据
   */
  create(data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/main-products`,
      method: 'post',
      data
    })
  },

  /**
   * 更新主营产品
   * @param {number} id 主营产品ID
   * @param {Object} data 更新数据
   * @returns {Promise} 响应数据
   */
  update(id, data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/main-products/${id}`,
      method: 'put',
      data
    })
  },

  /**
   * 删除主营产品
   * @param {number} id 主营产品ID
   * @returns {Promise} 响应数据
   */
  delete(id) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/main-products/${id}`,
      method: 'delete'
    })
  },

  /**
   * 批量删除主营产品
   * @param {Array<number>} ids 主营产品ID数组
   * @returns {Promise} 响应数据
   */
  batchDelete(ids) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/main-products/batch-delete`,
      method: 'post',
      data: { ids }
    })
  }
}
