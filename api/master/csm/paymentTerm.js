import { $fetch } from 'ofetch'

const BASE_URL = '/api/master/csm/payment-terms'

/**
 * 付款条件API
 */
export const paymentTermApi = {
  /**
   * 获取付款条件列表
   * @param {Object} params 查询参数
   * @returns {Promise} 响应数据
   */
  getList(params = {}) {
    return $fetch(BASE_URL, {
      method: 'GET',
      params
    })
  },

  /**
   * 根据ID获取付款条件详情
   * @param {number} id 付款条件ID
   * @returns {Promise} 响应数据
   */
  getById(id) {
    return $fetch(`${BASE_URL}/${id}`, {
      method: 'GET'
    })
  },

  /**
   * 创建付款条件
   * @param {Object} data 付款条件数据
   * @returns {Promise} 响应数据
   */
  create(data) {
    return $fetch(BASE_URL, {
      method: 'POST',
      body: data
    })
  },

  /**
   * 更新付款条件
   * @param {number} id 付款条件ID
   * @param {Object} data 更新数据
   * @returns {Promise} 响应数据
   */
  update(id, data) {
    return $fetch(`${BASE_URL}/${id}`, {
      method: 'PUT',
      body: data
    })
  },

  /**
   * 删除付款条件
   * @param {number} id 付款条件ID
   * @returns {Promise} 响应数据
   */
  delete(id) {
    return $fetch(`${BASE_URL}/${id}`, {
      method: 'DELETE'
    })
  },

  /**
   * 批量删除付款条件
   * @param {Array<number>} ids 付款条件ID数组
   * @returns {Promise} 响应数据
   */
  batchDelete(ids) {
    return $fetch(`${BASE_URL}/batch-delete`, {
      method: 'POST',
      body: { ids }
    })
  }
}
