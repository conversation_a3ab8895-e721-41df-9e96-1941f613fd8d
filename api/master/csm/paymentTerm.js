import { request } from '@/utils/request.js'
import { apiBaseUrl } from '@/api/config'

/**
 * 付款条件API
 */
export const paymentTermApi = {
  /**
   * 获取付款条件列表
   * @param {Object} params 查询参数
   * @returns {Promise} 响应数据
   */
  getList(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/payment-terms`,
      method: 'get',
      params
    })
  },

  /**
   * 根据ID获取付款条件详情
   * @param {number} id 付款条件ID
   * @returns {Promise} 响应数据
   */
  getById(id) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/payment-terms/${id}`,
      method: 'get'
    })
  },

  /**
   * 创建付款条件
   * @param {Object} data 付款条件数据
   * @returns {Promise} 响应数据
   */
  create(data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/payment-terms`,
      method: 'post',
      data
    })
  },

  /**
   * 更新付款条件
   * @param {number} id 付款条件ID
   * @param {Object} data 更新数据
   * @returns {Promise} 响应数据
   */
  update(id, data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/payment-terms/${id}`,
      method: 'put',
      data
    })
  },

  /**
   * 删除付款条件
   * @param {number} id 付款条件ID
   * @returns {Promise} 响应数据
   */
  delete(id) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/payment-terms/${id}`,
      method: 'delete'
    })
  },

  /**
   * 批量删除付款条件
   * @param {Array<number>} ids 付款条件ID数组
   * @returns {Promise} 响应数据
   */
  batchDelete(ids) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/payment-terms/batch-delete`,
      method: 'post',
      data: { ids }
    })
  }
}
