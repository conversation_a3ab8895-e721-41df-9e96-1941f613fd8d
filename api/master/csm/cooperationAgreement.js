import { $fetch } from 'ofetch'

const BASE_URL = '/api/master/csm/cooperation-agreements'

/**
 * 合作协议API
 */
export const cooperationAgreementApi = {
  /**
   * 获取合作协议列表
   * @param {Object} params 查询参数
   * @returns {Promise} 响应数据
   */
  getList(params = {}) {
    return $fetch(BASE_URL, {
      method: 'GET',
      params
    })
  },

  /**
   * 根据ID获取合作协议详情
   * @param {number} id 合作协议ID
   * @returns {Promise} 响应数据
   */
  getById(id) {
    return $fetch(`${BASE_URL}/${id}`, {
      method: 'GET'
    })
  },

  /**
   * 创建合作协议
   * @param {Object} data 合作协议数据
   * @returns {Promise} 响应数据
   */
  create(data) {
    return $fetch(BASE_URL, {
      method: 'POST',
      body: data
    })
  },

  /**
   * 更新合作协议
   * @param {number} id 合作协议ID
   * @param {Object} data 更新数据
   * @returns {Promise} 响应数据
   */
  update(id, data) {
    return $fetch(`${BASE_URL}/${id}`, {
      method: 'PUT',
      body: data
    })
  },

  /**
   * 删除合作协议
   * @param {number} id 合作协议ID
   * @returns {Promise} 响应数据
   */
  delete(id) {
    return $fetch(`${BASE_URL}/${id}`, {
      method: 'DELETE'
    })
  },

  /**
   * 批量删除合作协议
   * @param {Array<number>} ids 合作协议ID数组
   * @returns {Promise} 响应数据
   */
  batchDelete(ids) {
    return $fetch(`${BASE_URL}/batch-delete`, {
      method: 'POST',
      body: { ids }
    })
  }
}
