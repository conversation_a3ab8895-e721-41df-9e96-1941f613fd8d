import { request } from '@/utils/request.js'
import { apiBaseUrl } from '@/api/config'

/**
 * 合作协议API
 */
export const cooperationAgreementApi = {
  /**
   * 获取合作协议列表
   * @param {Object} params 查询参数
   * @returns {Promise} 响应数据
   */
  getList(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/cooperation-agreements`,
      method: 'get',
      params
    })
  },

  /**
   * 根据ID获取合作协议详情
   * @param {number} id 合作协议ID
   * @returns {Promise} 响应数据
   */
  getById(id) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/cooperation-agreements/${id}`,
      method: 'get'
    })
  },

  /**
   * 创建合作协议
   * @param {Object} data 合作协议数据
   * @returns {Promise} 响应数据
   */
  create(data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/cooperation-agreements`,
      method: 'post',
      data
    })
  },

  /**
   * 更新合作协议
   * @param {number} id 合作协议ID
   * @param {Object} data 更新数据
   * @returns {Promise} 响应数据
   */
  update(id, data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/cooperation-agreements/${id}`,
      method: 'put',
      data
    })
  },

  /**
   * 删除合作协议
   * @param {number} id 合作协议ID
   * @returns {Promise} 响应数据
   */
  delete(id) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/cooperation-agreements/${id}`,
      method: 'delete'
    })
  },

  /**
   * 批量删除合作协议
   * @param {Array<number>} ids 合作协议ID数组
   * @returns {Promise} 响应数据
   */
  batchDelete(ids) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/cooperation-agreements/batch-delete`,
      method: 'post',
      data: { ids }
    })
  }
}
