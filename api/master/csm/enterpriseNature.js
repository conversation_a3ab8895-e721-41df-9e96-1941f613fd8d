import { $fetch } from 'ofetch'

const BASE_URL = '/api/master/csm/enterprise-nature'

/**
 * 企业性质API
 */
export const enterpriseNatureApi = {
  /**
   * 获取企业性质列表
   * @param {Object} params 查询参数
   * @returns {Promise} 响应数据
   */
  getList(params = {}) {
    return $fetch(BASE_URL, {
      method: 'GET',
      params
    })
  },

  /**
   * 根据ID获取企业性质详情
   * @param {number} id 企业性质ID
   * @returns {Promise} 响应数据
   */
  getById(id) {
    return $fetch(`${BASE_URL}/${id}`, {
      method: 'GET'
    })
  },

  /**
   * 创建企业性质
   * @param {Object} data 企业性质数据
   * @returns {Promise} 响应数据
   */
  create(data) {
    return $fetch(BASE_URL, {
      method: 'POST',
      body: data
    })
  },

  /**
   * 更新企业性质
   * @param {number} id 企业性质ID
   * @param {Object} data 更新数据
   * @returns {Promise} 响应数据
   */
  update(id, data) {
    return $fetch(`${BASE_URL}/${id}`, {
      method: 'PUT',
      body: data
    })
  },

  /**
   * 删除企业性质
   * @param {number} id 企业性质ID
   * @returns {Promise} 响应数据
   */
  delete(id) {
    return $fetch(`${BASE_URL}/${id}`, {
      method: 'DELETE'
    })
  },

  /**
   * 批量删除企业性质
   * @param {Array<number>} ids 企业性质ID数组
   * @returns {Promise} 响应数据
   */
  batchDelete(ids) {
    return $fetch(`${BASE_URL}/batch-delete`, {
      method: 'POST',
      body: { ids }
    })
  }
}
