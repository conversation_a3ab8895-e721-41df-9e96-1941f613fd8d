import { request } from '@/utils/request.js'
import { apiBaseUrl } from '@/api/config'

/**
 * 企业性质API
 */
export const enterpriseNatureApi = {
  /**
   * 获取企业性质列表
   * @param {Object} params 查询参数
   * @returns {Promise} 响应数据
   */
  getList(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/enterprise-nature`,
      method: 'get',
      params
    })
  },

  /**
   * 根据ID获取企业性质详情
   * @param {number} id 企业性质ID
   * @returns {Promise} 响应数据
   */
  getById(id) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/enterprise-nature/${id}`,
      method: 'get'
    })
  },

  /**
   * 创建企业性质
   * @param {Object} data 企业性质数据
   * @returns {Promise} 响应数据
   */
  create(data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/enterprise-nature`,
      method: 'post',
      data
    })
  },

  /**
   * 更新企业性质
   * @param {number} id 企业性质ID
   * @param {Object} data 更新数据
   * @returns {Promise} 响应数据
   */
  update(id, data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/enterprise-nature/${id}`,
      method: 'put',
      data
    })
  },

  /**
   * 删除企业性质
   * @param {number} id 企业性质ID
   * @returns {Promise} 响应数据
   */
  delete(id) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/enterprise-nature/${id}`,
      method: 'delete'
    })
  },

  /**
   * 批量删除企业性质
   * @param {Array<number>} ids 企业性质ID数组
   * @returns {Promise} 响应数据
   */
  batchDelete(ids) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/enterprise-nature/batch-delete`,
      method: 'post',
      data: { ids }
    })
  }
}
